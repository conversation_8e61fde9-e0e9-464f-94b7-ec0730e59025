(function e(t,r,n){function i(s,a){if(!r[s]){if(!t[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);var f=new Error("Cannot find module '"+s+"'");throw f.code="MODULE_NOT_FOUND",f}var l=r[s]={exports:{}};t[s][0].call(l.exports,(function(e){var r=t[s][1][e];return i(r||e)}),l,l.exports,e,t,r,n)}return r[s].exports}for(var o="function"==typeof require&&require,s=0;s<n.length;s++)i(n[s]);return i})({1:[function(e,t,r){"use strict";(function(e){function n(e,t,r){var n,i,o,s,a,p,d,y,g=0,m=[],w=0,b=!1,v=!1,E=[],T=[],_=!1;if(r=r||{},n=r.encoding||"UTF8",y=r.numRounds||1,o=c(t,n),y!==parseInt(y,10)||1>y)throw Error("numRounds must a integer >= 1");if("SHA-1"===e)a=512,p=k,d=M,s=160;else if(p=function(t,r){return D(t,r,e)},d=function(t,r,n,i){var o,s;if("SHA-224"===e||"SHA-256"===e)o=15+(r+65>>>9<<4),s=16;else{if("SHA-384"!==e&&"SHA-512"!==e)throw Error("Unexpected error in SHA-2 implementation");o=31+(r+129>>>10<<5),s=32}for(;t.length<=o;)t.push(0);for(t[r>>>5]|=128<<24-r%32,t[o]=r+n,n=t.length,r=0;r<n;r+=s)i=D(t.slice(r,r+s),i,e);if("SHA-224"===e)t=[i[0],i[1],i[2],i[3],i[4],i[5],i[6]];else if("SHA-256"===e)t=i;else if("SHA-384"===e)t=[i[0].a,i[0].b,i[1].a,i[1].b,i[2].a,i[2].b,i[3].a,i[3].b,i[4].a,i[4].b,i[5].a,i[5].b];else{if("SHA-512"!==e)throw Error("Unexpected error in SHA-2 implementation");t=[i[0].a,i[0].b,i[1].a,i[1].b,i[2].a,i[2].b,i[3].a,i[3].b,i[4].a,i[4].b,i[5].a,i[5].b,i[6].a,i[6].b,i[7].a,i[7].b]}return t},"SHA-224"===e)a=512,s=224;else if("SHA-256"===e)a=512,s=256;else if("SHA-384"===e)a=1024,s=384;else{if("SHA-512"!==e)throw Error("Chosen SHA variant is not supported");a=1024,s=512}i=U(e),this.setHMACKey=function(t,r,o){var s;if(!0===v)throw Error("HMAC key already set");if(!0===b)throw Error("Cannot set HMAC key after finalizing hash");if(!0===_)throw Error("Cannot set HMAC key after calling update");if(n=(o||{}).encoding||"UTF8",r=c(r,n)(t),t=r.binLen,r=r.value,s=a>>>3,o=s/4-1,s<t/8){for(r=d(r,t,0,U(e));r.length<=o;)r.push(0);r[o]&=4294967040}else if(s>t/8){for(;r.length<=o;)r.push(0);r[o]&=4294967040}for(t=0;t<=o;t+=1)E[t]=909522486^r[t],T[t]=1549556828^r[t];i=p(E,i),g=a,v=!0},this.update=function(e){var t,r,n,s=0,u=a>>>5;for(t=o(e,m,w),e=t.binLen,r=t.value,t=e>>>5,n=0;n<t;n+=u)s+a<=e&&(i=p(r.slice(n,n+u),i),s+=a);g+=s,m=r.slice(s>>>5),w=e%a,_=!0},this.getHash=function(t,r){var n,o,a;if(!0===v)throw Error("Cannot call getHash after setting HMAC key");switch(a=h(r),t){case"HEX":n=function(e){return u(e,a)};break;case"B64":n=function(e){return f(e,a)};break;case"BYTES":n=l;break;default:throw Error("format must be HEX, B64, or BYTES")}if(!1===b)for(i=d(m,w,g,i),o=1;o<y;o+=1)i=d(i,s,0,U(e));return b=!0,n(i)},this.getHMAC=function(t,r){var n,o,c;if(!1===v)throw Error("Cannot call getHMAC without first setting HMAC key");switch(c=h(r),t){case"HEX":n=function(e){return u(e,c)};break;case"B64":n=function(e){return f(e,c)};break;case"BYTES":n=l;break;default:throw Error("outputFormat must be HEX, B64, or BYTES")}return!1===b&&(o=d(m,w,g,i),i=p(T,U(e)),i=d(o,s,a,i)),b=!0,n(i)}}function i(e,t){this.a=e,this.b=t}function o(e,t,r){var n,i,o,s,a,u=e.length;if(t=t||[0],r=r||0,a=r>>>3,0!==u%2)throw Error("String of HEX type must be in byte increments");for(n=0;n<u;n+=2){if(i=parseInt(e.substr(n,2),16),isNaN(i))throw Error("String of HEX type contains invalid characters");for(s=(n>>>1)+a,o=s>>>2;t.length<=o;)t.push(0);t[o]|=i<<8*(3-s%4)}return{value:t,binLen:4*u+r}}function s(e,t,r){var n,i,o,s,a=[];a=t||[0];for(r=r||0,i=r>>>3,n=0;n<e.length;n+=1)t=e.charCodeAt(n),s=n+i,o=s>>>2,a.length<=o&&a.push(0),a[o]|=t<<8*(3-s%4);return{value:a,binLen:8*e.length+r}}function a(e,t,r){var n,i,o,s,a,u,f=[],l=0;f=t||[0];if(r=r||0,t=r>>>3,-1===e.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");if(i=e.indexOf("="),e=e.replace(/\=/g,""),-1!==i&&i<e.length)throw Error("Invalid '=' found in base-64 string");for(i=0;i<e.length;i+=4){for(a=e.substr(i,4),o=s=0;o<a.length;o+=1)n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a[o]),s|=n<<18-6*o;for(o=0;o<a.length-1;o+=1){for(u=l+t,n=u>>>2;f.length<=n;)f.push(0);f[n]|=(s>>>16-8*o&255)<<8*(3-u%4),l+=1}}return{value:f,binLen:8*l+r}}function u(e,t){var r,n,i="",o=4*e.length;for(r=0;r<o;r+=1)n=e[r>>>2]>>>8*(3-r%4),i+="0123456789abcdef".charAt(n>>>4&15)+"0123456789abcdef".charAt(15&n);return t.outputUpper?i.toUpperCase():i}function f(e,t){var r,n,i,o="",s=4*e.length;for(r=0;r<s;r+=3)for(i=r+1>>>2,n=e.length<=i?0:e[i],i=r+2>>>2,i=e.length<=i?0:e[i],i=(e[r>>>2]>>>8*(3-r%4)&255)<<16|(n>>>8*(3-(r+1)%4)&255)<<8|i>>>8*(3-(r+2)%4)&255,n=0;4>n;n+=1)8*r+6*n<=32*e.length?o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>>6*(3-n)&63):o+=t.b64Pad;return o}function l(e){var t,r,n="",i=4*e.length;for(t=0;t<i;t+=1)r=e[t>>>2]>>>8*(3-t%4)&255,n+=String.fromCharCode(r);return n}function h(e){var t={outputUpper:!1,b64Pad:"="};if(e=e||{},t.outputUpper=e.outputUpper||!1,!0===e.hasOwnProperty("b64Pad")&&(t.b64Pad=e.b64Pad),"boolean"!==typeof t.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof t.b64Pad)throw Error("Invalid b64Pad formatting option");return t}function c(e,t){var r;switch(t){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(e){case"HEX":r=o;break;case"TEXT":r=function(e,r,n){var i,o,s,a,u,f=[],l=[],h=0;f=r||[0];if(r=n||0,s=r>>>3,"UTF8"===t)for(i=0;i<e.length;i+=1)for(n=e.charCodeAt(i),l=[],128>n?l.push(n):2048>n?(l.push(192|n>>>6),l.push(128|63&n)):55296>n||57344<=n?l.push(224|n>>>12,128|n>>>6&63,128|63&n):(i+=1,n=65536+((1023&n)<<10|1023&e.charCodeAt(i)),l.push(240|n>>>18,128|n>>>12&63,128|n>>>6&63,128|63&n)),o=0;o<l.length;o+=1){for(u=h+s,a=u>>>2;f.length<=a;)f.push(0);f[a]|=l[o]<<8*(3-u%4),h+=1}else if("UTF16BE"===t||"UTF16LE"===t)for(i=0;i<e.length;i+=1){for(n=e.charCodeAt(i),"UTF16LE"===t&&(o=255&n,n=o<<8|n>>>8),u=h+s,a=u>>>2;f.length<=a;)f.push(0);f[a]|=n<<8*(2-u%4),h+=2}return{value:f,binLen:8*h+r}};break;case"B64":r=a;break;case"BYTES":r=s;break;default:throw Error("format must be HEX, TEXT, B64, or BYTES")}return r}function p(e,t){return e<<t|e>>>32-t}function d(e,t){return e>>>t|e<<32-t}function y(e,t){var r=null;r=new i(e.a,e.b);return 32>=t?new i(r.a>>>t|r.b<<32-t&4294967295,r.b>>>t|r.a<<32-t&4294967295):new i(r.b>>>t-32|r.a<<64-t&4294967295,r.a>>>t-32|r.b<<64-t&4294967295)}function g(e,t){return 32>=t?new i(e.a>>>t,e.b>>>t|e.a<<32-t&4294967295):new i(0,e.a>>>t-32)}function m(e,t,r){return e&t^~e&r}function w(e,t,r){return new i(e.a&t.a^~e.a&r.a,e.b&t.b^~e.b&r.b)}function b(e,t,r){return e&t^e&r^t&r}function v(e,t,r){return new i(e.a&t.a^e.a&r.a^t.a&r.a,e.b&t.b^e.b&r.b^t.b&r.b)}function E(e){return d(e,2)^d(e,13)^d(e,22)}function T(e){var t=y(e,28),r=y(e,34);return e=y(e,39),new i(t.a^r.a^e.a,t.b^r.b^e.b)}function _(e){return d(e,6)^d(e,11)^d(e,25)}function S(e){var t=y(e,14),r=y(e,18);return e=y(e,41),new i(t.a^r.a^e.a,t.b^r.b^e.b)}function R(e){return d(e,7)^d(e,18)^e>>>3}function C(e){var t=y(e,1),r=y(e,8);return e=g(e,7),new i(t.a^r.a^e.a,t.b^r.b^e.b)}function L(e){return d(e,17)^d(e,19)^e>>>10}function I(e){var t=y(e,19),r=y(e,61);return e=g(e,6),new i(t.a^r.a^e.a,t.b^r.b^e.b)}function A(e,t){var r=(65535&e)+(65535&t);return((e>>>16)+(t>>>16)+(r>>>16)&65535)<<16|65535&r}function B(e,t,r,n){var i=(65535&e)+(65535&t)+(65535&r)+(65535&n);return((e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(i>>>16)&65535)<<16|65535&i}function x(e,t,r,n,i){var o=(65535&e)+(65535&t)+(65535&r)+(65535&n)+(65535&i);return((e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(i>>>16)+(o>>>16)&65535)<<16|65535&o}function N(e,t){var r,n,o;return r=(65535&e.b)+(65535&t.b),n=(e.b>>>16)+(t.b>>>16)+(r>>>16),o=(65535&n)<<16|65535&r,r=(65535&e.a)+(65535&t.a)+(n>>>16),n=(e.a>>>16)+(t.a>>>16)+(r>>>16),new i((65535&n)<<16|65535&r,o)}function P(e,t,r,n){var o,s,a;return o=(65535&e.b)+(65535&t.b)+(65535&r.b)+(65535&n.b),s=(e.b>>>16)+(t.b>>>16)+(r.b>>>16)+(n.b>>>16)+(o>>>16),a=(65535&s)<<16|65535&o,o=(65535&e.a)+(65535&t.a)+(65535&r.a)+(65535&n.a)+(s>>>16),s=(e.a>>>16)+(t.a>>>16)+(r.a>>>16)+(n.a>>>16)+(o>>>16),new i((65535&s)<<16|65535&o,a)}function O(e,t,r,n,o){var s,a,u;return s=(65535&e.b)+(65535&t.b)+(65535&r.b)+(65535&n.b)+(65535&o.b),a=(e.b>>>16)+(t.b>>>16)+(r.b>>>16)+(n.b>>>16)+(o.b>>>16)+(s>>>16),u=(65535&a)<<16|65535&s,s=(65535&e.a)+(65535&t.a)+(65535&r.a)+(65535&n.a)+(65535&o.a)+(a>>>16),a=(e.a>>>16)+(t.a>>>16)+(r.a>>>16)+(n.a>>>16)+(o.a>>>16)+(s>>>16),new i((65535&a)<<16|65535&s,u)}function U(e){var t,r;if("SHA-1"===e)e=[1732584193,4023233417,2562383102,271733878,3285377520];else switch(t=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],r=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],e){case"SHA-224":e=t;break;case"SHA-256":e=r;break;case"SHA-384":e=[new i(3418070365,t[0]),new i(1654270250,t[1]),new i(2438529370,t[2]),new i(355462360,t[3]),new i(1731405415,t[4]),new i(41048885895,t[5]),new i(3675008525,t[6]),new i(1203062813,t[7])];break;case"SHA-512":e=[new i(r[0],4089235720),new i(r[1],2227873595),new i(r[2],4271175723),new i(r[3],1595750129),new i(r[4],2917565137),new i(r[5],725511199),new i(r[6],4215389547),new i(r[7],327033209)];break;default:throw Error("Unknown SHA variant")}return e}function k(e,t){var r,n,i,o,s,a,u,f=[];for(r=t[0],n=t[1],i=t[2],o=t[3],s=t[4],u=0;80>u;u+=1)f[u]=16>u?e[u]:p(f[u-3]^f[u-8]^f[u-14]^f[u-16],1),a=20>u?x(p(r,5),n&i^~n&o,s,1518500249,f[u]):40>u?x(p(r,5),n^i^o,s,1859775393,f[u]):60>u?x(p(r,5),b(n,i,o),s,2400959708,f[u]):x(p(r,5),n^i^o,s,3395469782,f[u]),s=o,o=i,i=p(n,30),n=r,r=a;return t[0]=A(r,t[0]),t[1]=A(n,t[1]),t[2]=A(i,t[2]),t[3]=A(o,t[3]),t[4]=A(s,t[4]),t}function M(e,t,r,n){var i;for(i=15+(t+65>>>9<<4);e.length<=i;)e.push(0);for(e[t>>>5]|=128<<24-t%32,e[i]=t+r,r=e.length,t=0;t<r;t+=16)n=k(e.slice(t,t+16),n);return n}function D(e,t,r){var n,o,s,a,u,f,l,h,c,p,d,y,g,U,k,M,D,F,j,Y,H,G,z,W=[];if("SHA-224"===r||"SHA-256"===r)p=64,y=1,G=Number,g=A,U=B,k=x,M=R,D=L,F=E,j=_,H=b,Y=m,z=V;else{if("SHA-384"!==r&&"SHA-512"!==r)throw Error("Unexpected error in SHA-2 implementation");p=80,y=2,G=i,g=N,U=P,k=O,M=C,D=I,F=T,j=S,H=v,Y=w,z=q}for(r=t[0],n=t[1],o=t[2],s=t[3],a=t[4],u=t[5],f=t[6],l=t[7],d=0;d<p;d+=1)16>d?(c=d*y,h=e.length<=c?0:e[c],c=e.length<=c+1?0:e[c+1],W[d]=new G(h,c)):W[d]=U(D(W[d-2]),W[d-7],M(W[d-15]),W[d-16]),h=k(l,j(a),Y(a,u,f),z[d],W[d]),c=g(F(r),H(r,n,o)),l=f,f=u,u=a,a=g(s,h),s=o,o=n,n=r,r=g(h,c);return t[0]=g(r,t[0]),t[1]=g(n,t[1]),t[2]=g(o,t[2]),t[3]=g(s,t[3]),t[4]=g(a,t[4]),t[5]=g(u,t[5]),t[6]=g(f,t[6]),t[7]=g(l,t[7]),t}var V,q;V=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],q=[new i(V[0],3609767458),new i(V[1],602891725),new i(V[2],3964484399),new i(V[3],2173295548),new i(V[4],4081628472),new i(V[5],3053834265),new i(V[6],2937671579),new i(V[7],3664609560),new i(V[8],2734883394),new i(V[9],1164996542),new i(V[10],1323610764),new i(V[11],3590304994),new i(V[12],4068182383),new i(V[13],991336113),new i(V[14],633803317),new i(V[15],3479774868),new i(V[16],2666613458),new i(V[17],944711139),new i(V[18],2341262773),new i(V[19],2007800933),new i(V[20],1495990901),new i(V[21],1856431235),new i(V[22],3175218132),new i(V[23],2198950837),new i(V[24],3999719339),new i(V[25],766784016),new i(V[26],2566594879),new i(V[27],3203337956),new i(V[28],1034457026),new i(V[29],2466948901),new i(V[30],3758326383),new i(V[31],168717936),new i(V[32],1188179964),new i(V[33],1546045734),new i(V[34],1522805485),new i(V[35],2643833823),new i(V[36],2343527390),new i(V[37],1014477480),new i(V[38],1206759142),new i(V[39],344077627),new i(V[40],1290863460),new i(V[41],3158454273),new i(V[42],3505952657),new i(V[43],106217008),new i(V[44],3606008344),new i(V[45],1432725776),new i(V[46],1467031594),new i(V[47],851169720),new i(V[48],3100823752),new i(V[49],1363258195),new i(V[50],3750685593),new i(V[51],3785050280),new i(V[52],3318307427),new i(V[53],3812723403),new i(V[54],2003034995),new i(V[55],3602036899),new i(V[56],1575990012),new i(V[57],1125592928),new i(V[58],2716904306),new i(V[59],442776044),new i(V[60],593698344),new i(V[61],3733110249),new i(V[62],2999351573),new i(V[63],3815920427),new i(3391569614,3928383900),new i(3515267271,566280711),new i(3940187606,3454069534),new i(4118630271,4000239992),new i(116418474,1914138554),new i(174292421,2731055270),new i(289380356,3203993006),new i(460393269,320620315),new i(685471733,587496836),new i(852142971,1086792851),new i(1017036298,365543100),new i(1126000580,2618297676),new i(1288033470,3409855158),new i(1501505948,4234509866),new i(1607167915,987167468),new i(1816402316,1246189591)],"function"===typeof define&&define.amd?define((function(){return n})):"undefined"!==typeof r?"undefined"!==typeof t&&t.exports?t.exports=r=n:r=n:e.jsSHA=n})(this)},{}],2:[function(e,t,r){(function(e){if("object"===typeof r)t.exports=e();else if("function"===typeof define&&define.amd)define(e);else{var n;try{n=window}catch(i){n=self}n.SparkMD5=e()}})((function(e){"use strict";var t=function(e,t){return e+t&4294967295},r=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function n(e,r,n,i,o,s){return r=t(t(r,e),t(i,s)),t(r<<o|r>>>32-o,n)}function i(e,t,r,i,o,s,a){return n(t&r|~t&i,e,t,o,s,a)}function o(e,t,r,i,o,s,a){return n(t&i|r&~i,e,t,o,s,a)}function s(e,t,r,i,o,s,a){return n(t^r^i,e,t,o,s,a)}function a(e,t,r,i,o,s,a){return n(r^(t|~i),e,t,o,s,a)}function u(e,r){var n=e[0],u=e[1],f=e[2],l=e[3];n=i(n,u,f,l,r[0],7,-680876936),l=i(l,n,u,f,r[1],12,-389564586),f=i(f,l,n,u,r[2],17,606105819),u=i(u,f,l,n,r[3],22,-1044525330),n=i(n,u,f,l,r[4],7,-176418897),l=i(l,n,u,f,r[5],12,1200080426),f=i(f,l,n,u,r[6],17,-1473231341),u=i(u,f,l,n,r[7],22,-45705983),n=i(n,u,f,l,r[8],7,1770035416),l=i(l,n,u,f,r[9],12,-1958414417),f=i(f,l,n,u,r[10],17,-42063),u=i(u,f,l,n,r[11],22,-1990404162),n=i(n,u,f,l,r[12],7,1804603682),l=i(l,n,u,f,r[13],12,-40341101),f=i(f,l,n,u,r[14],17,-1502002290),u=i(u,f,l,n,r[15],22,1236535329),n=o(n,u,f,l,r[1],5,-165796510),l=o(l,n,u,f,r[6],9,-1069501632),f=o(f,l,n,u,r[11],14,643717713),u=o(u,f,l,n,r[0],20,-373897302),n=o(n,u,f,l,r[5],5,-701558691),l=o(l,n,u,f,r[10],9,38016083),f=o(f,l,n,u,r[15],14,-660478335),u=o(u,f,l,n,r[4],20,-405537848),n=o(n,u,f,l,r[9],5,568446438),l=o(l,n,u,f,r[14],9,-1019803690),f=o(f,l,n,u,r[3],14,-187363961),u=o(u,f,l,n,r[8],20,1163531501),n=o(n,u,f,l,r[13],5,-1444681467),l=o(l,n,u,f,r[2],9,-51403784),f=o(f,l,n,u,r[7],14,1735328473),u=o(u,f,l,n,r[12],20,-1926607734),n=s(n,u,f,l,r[5],4,-378558),l=s(l,n,u,f,r[8],11,-2022574463),f=s(f,l,n,u,r[11],16,1839030562),u=s(u,f,l,n,r[14],23,-35309556),n=s(n,u,f,l,r[1],4,-1530992060),l=s(l,n,u,f,r[4],11,1272893353),f=s(f,l,n,u,r[7],16,-155497632),u=s(u,f,l,n,r[10],23,-1094730640),n=s(n,u,f,l,r[13],4,681279174),l=s(l,n,u,f,r[0],11,-358537222),f=s(f,l,n,u,r[3],16,-722521979),u=s(u,f,l,n,r[6],23,76029189),n=s(n,u,f,l,r[9],4,-640364487),l=s(l,n,u,f,r[12],11,-421815835),f=s(f,l,n,u,r[15],16,530742520),u=s(u,f,l,n,r[2],23,-995338651),n=a(n,u,f,l,r[0],6,-198630844),l=a(l,n,u,f,r[7],10,1126891415),f=a(f,l,n,u,r[14],15,-1416354905),u=a(u,f,l,n,r[5],21,-57434055),n=a(n,u,f,l,r[12],6,1700485571),l=a(l,n,u,f,r[3],10,-1894986606),f=a(f,l,n,u,r[10],15,-1051523),u=a(u,f,l,n,r[1],21,-2054922799),n=a(n,u,f,l,r[8],6,1873313359),l=a(l,n,u,f,r[15],10,-30611744),f=a(f,l,n,u,r[6],15,-1560198380),u=a(u,f,l,n,r[13],21,1309151649),n=a(n,u,f,l,r[4],6,-145523070),l=a(l,n,u,f,r[11],10,-1120210379),f=a(f,l,n,u,r[2],15,718787259),u=a(u,f,l,n,r[9],21,-343485551),e[0]=t(n,e[0]),e[1]=t(u,e[1]),e[2]=t(f,e[2]),e[3]=t(l,e[3])}function f(e){var t,r=[];for(t=0;t<64;t+=4)r[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return r}function l(e){var t,r=[];for(t=0;t<64;t+=4)r[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return r}function h(e){var t,r,n,i,o,s,a=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=a;t+=64)u(l,f(e.substring(t-64,t)));for(e=e.substring(t-64),r=e.length,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<r;t+=1)n[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(n[t>>2]|=128<<(t%4<<3),t>55)for(u(l,n),t=0;t<16;t+=1)n[t]=0;return i=8*a,i=i.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,n[14]=o,n[15]=s,u(l,n),l}function c(e){var t,r,n,i,o,s,a=e.length,f=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=a;t+=64)u(f,l(e.subarray(t-64,t)));for(e=t-64<a?e.subarray(t-64):new Uint8Array(0),r=e.length,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<r;t+=1)n[t>>2]|=e[t]<<(t%4<<3);if(n[t>>2]|=128<<(t%4<<3),t>55)for(u(f,n),t=0;t<16;t+=1)n[t]=0;return i=8*a,i=i.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,n[14]=o,n[15]=s,u(f,n),f}function p(e){var t,n="";for(t=0;t<4;t+=1)n+=r[e>>8*t+4&15]+r[e>>8*t&15];return n}function d(e){var t;for(t=0;t<e.length;t+=1)e[t]=p(e[t]);return e.join("")}function y(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),e}function g(e,t){var r,n=e.length,i=new ArrayBuffer(n),o=new Uint8Array(i);for(r=0;r<n;r++)o[r]=e.charCodeAt(r);return t?o:i}function m(e){return String.fromCharCode.apply(null,new Uint8Array(e))}function w(e,t,r){var n=new Uint8Array(e.byteLength+t.byteLength);return n.set(new Uint8Array(e)),n.set(new Uint8Array(t),e.byteLength),r?n:n.buffer}function b(){this.reset()}return"5d41402abc4b2a76b9719d911017c592"!==d(h("hello"))&&(t=function(e,t){var r=(65535&e)+(65535&t),n=(e>>16)+(t>>16)+(r>>16);return n<<16|65535&r}),"undefined"===typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function t(e,t){return e=0|e||0,e<0?Math.max(e+t,0):Math.min(e,t)}ArrayBuffer.prototype.slice=function(r,n){var i,o,s,a,u=this.byteLength,f=t(r,u),l=u;return n!==e&&(l=t(n,u)),f>l?new ArrayBuffer(0):(i=l-f,o=new ArrayBuffer(i),s=new Uint8Array(o),a=new Uint8Array(this,f,i),s.set(a),o)}}(),b.prototype.append=function(e){return this.appendBinary(y(e)),this},b.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,r=this._buff.length;for(t=64;t<=r;t+=64)u(this._hash,f(this._buff.substring(t-64,t)));return this._buff=this._buff.substring(t-64),this},b.prototype.end=function(e){var t,r,n=this._buff,i=n.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=n.charCodeAt(t)<<(t%4<<3);return this._finish(o,i),r=e?this._hash:d(this._hash),this.reset(),r},b.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},b.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash}},b.prototype.setState=function(e){return this._buff=e.buff,this._length=e.length,this._hash=e.hash,this},b.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},b.prototype._finish=function(e,t){var r,n,i,o=t;if(e[o>>2]|=128<<(o%4<<3),o>55)for(u(this._hash,e),o=0;o<16;o+=1)e[o]=0;r=8*this._length,r=r.toString(16).match(/(.*?)(.{0,8})$/),n=parseInt(r[2],16),i=parseInt(r[1],16)||0,e[14]=n,e[15]=i,u(this._hash,e)},b.hash=function(e,t){return b.hashBinary(y(e),t)},b.hashBinary=function(e,t){var r=h(e);return t?r:d(r)},b.ArrayBuffer=function(){this.reset()},b.ArrayBuffer.prototype.append=function(e){var t,r=w(this._buff.buffer,e,!0),n=r.length;for(this._length+=e.byteLength,t=64;t<=n;t+=64)u(this._hash,l(r.subarray(t-64,t)));return this._buff=t-64<n?new Uint8Array(r.buffer.slice(t-64)):new Uint8Array(0),this},b.ArrayBuffer.prototype.end=function(e){var t,r,n=this._buff,i=n.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=n[t]<<(t%4<<3);return this._finish(o,i),r=e?this._hash:d(this._hash),this.reset(),r},b.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},b.ArrayBuffer.prototype.getState=function(){var e=b.prototype.getState.call(this);return e.buff=m(e.buff),e},b.ArrayBuffer.prototype.setState=function(e){return e.buff=g(e.buff,!0),b.prototype.setState.call(this,e)},b.ArrayBuffer.prototype.destroy=b.prototype.destroy,b.ArrayBuffer.prototype._finish=b.prototype._finish,b.ArrayBuffer.hash=function(e,t){var r=c(new Uint8Array(e));return t?r:d(r)},b}))},{}],3:[function(e,t,r){window.TableStore=t.exports=e("./lib/core"),e("./lib/http/xhr")},{"./lib/core":6,"./lib/http/xhr":11}],4:[function(e,t,r){var n=e("./core"),i=n.util.inherit;n.Client=i({constructor:function(e){this.config=new n.Config(e)},setupRequestListeners:function(e){e.addListener("build",this.populateHeader),e.addListener("build",this.populateURI),e.addListener("build",this.buildContent),e.addListener("build",this.computeContentMd5),e.addListener("extractError",this.extractError),e.addListener("extractData",this.extractData)},populateURI:function(e){var t=e.httpRequest;t.endpoint.host=t.endpoint.hostname,t.path="/"+n.util.string.upperFirst(e.operation)},populateHeader:function(e){var t=e.httpRequest;t.headers["x-ots-apiversion"]="2015-12-31",t.headers["x-ots-instancename"]=e.config.instancename},buildContent:function(e){var t=n.encoder.encode(e.operation,e.params),r=t.encode();e.httpRequest.body=r.toBuffer()},computeContentMd5:function(e){var t=n.util.crypto.md5(e.httpRequest.body,"base64");e.httpRequest.headers["x-ots-contentmd5"]=t},extractData:function(e){e.data=n.decoder.decode(e.request.operation,e.httpResponse.body),e.data.RequestId=e.httpResponse.headers["x-ots-request-id"]||e.httpResponse.headers["x-ots-requestid"]},extractError:function(e){var t,r={304:"NotModified",403:"Forbidden",400:"BadRequest",404:"NotFound"},i=e.httpResponse.statusCode,o=e.httpResponse.body;if(r[i]&&0===o.length)e.error=n.util.error(new Error,{code:r[e.httpResponse.statusCode],message:null,headers:e.httpResponse.headers});else try{t=new n.XML.Parser({}).parse(o.toString()),e.error=n.util.error(new Error,{code:t.Code||i,message:t.Message||null,headers:e.httpResponse.headers})}catch(s){t=o.toString(),e.error=n.util.error(new Error,{code:i,message:t,headers:e.httpResponse.headers})}},makeRequest:function(e,t,r){"function"===typeof t&&(r=t,t=null);var i=new n.Request(this.config,e,t);return this.addAllRequestListeners(i),r?(i.send(r),i):new Promise((function(e,t){i.send((function(r,n){if(r)return t(r);e(n)}))}))},addAllRequestListeners:function(e){for(var t=[n.events,n.EventListeners.Core],r=0;r<t.length;r++)t[r]&&e.addListeners(t[r]);this.config.logger&&e.addListeners(n.EventListeners.Logger),this.setupRequestListeners(e)},createTable:function(e,t){return this.makeRequest("createTable",e,t)},listTable:function(e,t){return this.makeRequest("listTable",e,t)},deleteTable:function(e,t){return this.makeRequest("deleteTable",e,t)},updateTable:function(e,t){return this.makeRequest("updateTable",e,t)},describeTable:function(e,t){return this.makeRequest("describeTable",e,t)},getRow:function(e,t){return this.makeRequest("getRow",e,t)},putRow:function(e,t){return this.makeRequest("putRow",e,t)},updateRow:function(e,t){return this.makeRequest("updateRow",e,t)},deleteRow:function(e,t){return this.makeRequest("deleteRow",e,t)},getRange:function(e,t){return this.makeRequest("getRange",e,t)},batchGetRow:function(e,t){return this.makeRequest("batchGetRow",e,t)},batchWriteRow:function(e,t){return this.makeRequest("batchWriteRow",e,t)}})},{"./core":6}],5:[function(e,t,r){var n=e("./core");n.Config=n.util.inherit({constructor:function(e){void 0===e&&(e={}),n.util.each.call(this,this.keys,(function(t,r){var n=e[t];"string"===typeof e[t]&&(n=e[t].replace(/^\s+/g,"")),this.set(t,n,r)}))},clear:function(){n.util.each.call(this,this.keys,(function(e){delete this[e]})),this.set("credentials",void 0),this.set("credentialProvider",void 0)},getCredentials:function(){return{accessKeyId:this.accessKeyId,secretAccessKey:this.secretAccessKey,securityToken:this.stsToken}},set:function(e,t,r){void 0===t?(void 0===r&&(r=this.keys[e]),this[e]="function"===typeof r?r.call(this):r):this[e]=t},keys:{accessKeyId:null,secretAccessKey:null,stsToken:null,logger:null,endpoint:void 0,httpOptions:{},maxRetries:void 0,instancename:void 0,computeChecksums:!0}})},{"./core":6}],6:[function(e,t,r){var n={};t.exports=n,e("./util"),e("./metadata"),e("./long"),e("./protocol/plain_buffer_consts"),e("./protocol/plain_buffer_crc8"),e("./protocol/plain_buffer_stream"),e("./protocol/plain_buffer_coded_stream"),e("./protocol/plian_buffer_builder"),e("./filter"),e("./protocol/encoder"),e("./protocol/decoder"),e("./metadata"),e("./config"),e("./http"),e("./sequential_executor"),e("./event_listeners"),e("./request"),e("./signer"),n.events=new n.SequentialExecutor,e("./http/node"),e("./retry/retry_util"),e("./retry/default_retry_policy"),e("./client")},{"./client":4,"./config":5,"./event_listeners":7,"./filter":8,"./http":9,"./http/node":10,"./long":12,"./metadata":13,"./protocol/decoder":14,"./protocol/encoder":15,"./protocol/plain_buffer_coded_stream":16,"./protocol/plain_buffer_consts":17,"./protocol/plain_buffer_crc8":18,"./protocol/plain_buffer_stream":19,"./protocol/plian_buffer_builder":20,"./request":23,"./retry/default_retry_policy":24,"./retry/retry_util":25,"./sequential_executor":26,"./signer":27,"./util":28}],7:[function(e,t,r){var n=e("./core");e("./sequential_executor"),n.EventListeners={Core:{}},n.EventListeners={Core:(new n.SequentialExecutor).addNamedListeners((function(e,t){e("SET_CONTENT_LENGTH","afterBuild",(function(e){if(void 0===e.httpRequest.headers["Content-Length"]){var t=n.util.string.byteLength(e.httpRequest.body);e.httpRequest.headers["Content-Length"]=t}})),e("SET_HTTP_HOST","afterBuild",(function(e){e.httpRequest.headers["Host"]=e.httpRequest.endpoint.host})),t("SIGN","sign",(function(e,t){var r=e.config.getCredentials();try{var i=n.util.date.getDate(),o=new n.Signer(e.httpRequest);o.addAuthorization(r,i)}catch(s){e.response.error=s}t()})),e("VALIDATE_RESPONSE","validateResponse",(function(e){e.httpResponse.statusCode<300?(e.data={},e.error=null):(e.data=null,e.error=n.util.error(new Error,{code:"UnknownError",message:"An unknown error occurred."}))})),t("SEND","send",(function(e,t){function r(r){e.httpResponse.stream=r,e.httpResponse._abortCallback=t,r.on("headers",(function(t,i){e.request.emit("httpHeaders",[t,i,e]),e.request.httpRequest._streaming||(2===n.HttpClient.streamsApiVersion?r.on("readable",(function(){var t=r.read();null!==t&&e.request.emit("httpData",[t,e])})):r.on("data",(function(t){e.request.emit("httpData",[t,e])})))})),r.on("end",(function(){e.request.emit("httpDone"),t()}))}function i(t){t.on("sendProgress",(function(t){e.request.emit("httpUploadProgress",[t,e])})),t.on("receiveProgress",(function(t){e.request.emit("httpDownloadProgress",[t,e])}))}function o(r){e.error=n.util.error(r,{code:"NetworkingError",region:e.request.httpRequest.region,hostname:e.request.httpRequest.endpoint.hostname,retryable:!0}),e.request.emit("httpError",[e.error,e],(function(){t()}))}e.error=null,e.data=null;var s=n.HttpClient.getInstance(),a=e.request.config.httpOptions||{},u=s.handleRequest(this.httpRequest,a,r,o);i(u)})),e("HTTP_HEADERS","httpHeaders",(function(e,t,r){r.httpResponse.statusCode=e,r.httpResponse.headers=t,r.httpResponse.body=new n.util.Buffer(""),r.httpResponse.buffers=[],r.httpResponse.numBytes=0})),e("HTTP_DATA","httpData",(function(e,t){if(e){t.httpResponse.numBytes+=e.length;var r=t.httpResponse.headers["content-length"],i={loaded:t.httpResponse.numBytes,total:r};t.request.emit("httpDownloadProgress",[i,t]),t.httpResponse.buffers.push(new n.util.Buffer(e))}})),e("HTTP_DONE","httpDone",(function(e){if(e.httpResponse.buffers&&e.httpResponse.buffers.length>0){var t=n.util.buffer.concat(e.httpResponse.buffers);e.httpResponse.body=t}delete e.httpResponse.numBytes,delete e.httpResponse.buffers})),e("RETRY_CHECK","retry",(function(e){e.error&&(e.error.retryable=n.DefaultRetryPolicy.shouldRetry(e.retryCount,e.error,e.request.operation))})),t("RESET_RETRY_STATE","afterRetry",(function(e,t){var r,i=!1;if(e.error){r=n.DefaultRetryPolicy.getRetryDelay(e.retryCount,e.error);var o=n.DefaultRetryPolicy.maxRetryTimes;e.error.retryable&&e.retryCount<o&&(e.retryCount++,i=!0)}i?(e.error=null,setTimeout(t,r)):t()}))})),Logger:(new n.SequentialExecutor).addNamedListeners((function(t){t("LOG_REQUEST","complete",(function(t){var r=t.request,i=r.config.logger;if(i){var o=s();"function"===typeof i.log?i.log(o):"function"===typeof i.write&&i.write(o+"\n")}function s(){var o=n.util.date.getDate().getTime(),s=(o-r.startTime.getTime())/1e3,a=!!i.isTTY,u=t.httpResponse.statusCode,f=e("util").inspect(r.params,!0,!0),l="";return a&&(l+="[33m"),l+="[TableStore "+r.service.serviceIdentifier+" "+u,l+=" "+s.toString()+"s "+t.retryCount+" retries]",a&&(l+="[0;1m"),l+=" "+r.operation+"("+f+")",a&&(l+="[0m"),l}}))}))}},{"./core":6,"./sequential_executor":26,util:81}],8:[function(e,t,r){var n=e("./core"),i=e("./protocol/tablestore_filter_proto.js").tablestore.filter.proto,o=n.util.inherit;n.LogicalOperator={NOT:i.LogicalOperator.LO_NOT,AND:i.LogicalOperator.LO_AND,OR:i.LogicalOperator.LO_OR},n.ColumnConditionType={COMPOSITE_COLUMN_CONDITION:0,SINGLE_COLUMN_CONDITION:1},n.ComparatorType={EQUAL:i.ComparatorType.CT_EQUAL,NOT_EQUAL:i.ComparatorType.CT_NOT_EQUAL,GREATER_THAN:i.ComparatorType.CT_GREATER_THAN,GREATER_EQUAL:i.ComparatorType.CT_GREATER_EQUAL,LESS_THAN:i.ComparatorType.CT_LESS_THAN,LESS_EQUAL:i.ComparatorType.CT_LESS_EQUAL},n.RowExistenceExpectation={IGNORE:"IGNORE",EXPECT_EXIST:"EXPECT_EXIST",EXPECT_NOT_EXIST:"EXPECT_NOT_EXIST"},n.ColumnCondition=o({}),n.CompositeCondition=o(n.ColumnCondition,{constructor:function(e){this.sub_conditions=[],this.setCombinator(e)},getType:function(){return i.FilterType.FT_COMPOSITE_COLUMN_VALUE},setCombinator:function(e){var t=!1;for(pro in n.LogicalOperator)if(n.LogicalOperator[pro]===e){t=!0;break}if(!t)throw new Error("Expect input combinator should be one of TableStore.LogicalOperator");this.combinator=e},getCombinator:function(){return combinator},addSubCondition:function(e){if(!e instanceof n.ColumnCondition)throw new Error("The input condition should be an instance of TableStore.ColumnCondition");this.sub_conditions.push(e)},clearSubCondition:function(){this.sub_conditions=[]}}),n.SingleColumnCondition=o(n.ColumnCondition,{constructor:function(e,t,r,n,i){void 0===n&&(n=!0),void 0===i&&(i=!0),this.columnName=e,this.columnValue=t,this.comparator=null,this.passIfMissing=null,this.latestVersionOnly=null,this.setComparator(r),this.setPassIfMissing(n),this.setLatestVersionOnly(i)},getType:function(){return i.FilterType.FT_SINGLE_COLUMN_VALUE},setPassIfMissing:function(e){this.passIfMissing=e},getPassIfMissing:function(){return this.passIfMissing},setLatestVersionOnly:function(e){this.latestVersionOnly=e},getLatestVersionOnly:function(){return this.latestVersionOnly},setColumnName:function(e){this.columnName=e},getColumnName:function(){return this.columnName},setColumnValue:function(e){this.columnValue=e},getColumnValue:function(){return this.columnValue},setComparator:function(e){var t=!1;for(pro in n.ComparatorType)if(n.ComparatorType[pro]===e){t=!0;break}if(!t)throw new Error("Expect input comparator should be one of TableStore.ComparatorType");this.comparator=e},getComparator:function(){return this.comparator}}),n.Condition=o({constructor:function(e,t){this.rowExistenceExpectation=null,this.columnCondition=null,void 0===t&&(t=null),this.setRowExistenceExpectation(e),null!=t&&this.setColumnCondition(t)},setRowExistenceExpectation:function(e){var t=!1;for(pro in n.RowExistenceExpectation)if(n.RowExistenceExpectation[pro]===e){t=!0;break}if(!t)throw new Error("Expect input rowExistenceExpectation should be one of TableStore.RowExistenceExpectation");this.rowExistenceExpectation=e},getRowExistenceExpectation:function(){return this.rowExistenceExpectation},setColumnCondition:function(e){if(!e instanceof n.ColumnCondition)throw new Error("The input columnCondition should be an instance of TableStore.ColumnCondition");this.columnCondition=e},getColumnCondition:function(){this.columnCondition}}),n.ColumnPaginationFilter=o({constructor:function(e,t){this.limit=void 0===e?1:e,this.offset=void 0===t?0:t},getType:function(){return i.FilterType.FT_COLUMN_PAGINATION}})},{"./core":6,"./protocol/tablestore_filter_proto.js":21}],9:[function(e,t,r){(function(t){var r=e("./core"),n=r.util.inherit;r.Endpoint=n({constructor:function(e){if(r.util.hideProperties(this,["slashes","auth","hash","search","query"]),"undefined"===typeof e||null===e)throw new Error("Invalid endpoint: "+e);if(!e.match(/^http/))throw new Error("错误的 endpoint 格式, 需要以 http 或者 https 开头");r.util.update(this,r.util.urlParse(e)),this.port?this.port=parseInt(this.port,10):this.port="https:"===this.protocol?443:80}}),r.HttpRequest=n({constructor:function(e,t){this.method="POST",this.path=e.path||"/",this.headers={},this.body="",this.endpoint=e,this.region=t},pathname:function(){return this.path.split("?",1)[0]},search:function(){return this.path.split("?",2)[1]||""},debug:function(){if("aliyun"==t.env.DEBUG)for(var e in console.log("-------- HttpRequest Start: --------"),console.log("method:",this.method),console.log("path:",this.path),console.log("headers:"),this.headers)"constructor"!=e&&console.log(e,":",this.headers[e])}}),r.HttpResponse=n({constructor:function(){this.statusCode=void 0,this.headers={},this.body=void 0}}),r.HttpClient=n({}),r.HttpClient.getInstance=function(){return void 0===this.singleton&&(this.singleton=new this),this.singleton}}).call(this,e("_process"))},{"./core":6,_process:50}],10:[function(e,t,r){var n=e("../core"),i=e("stream").Stream,o=e("stream").Writable,s=e("stream").Readable;e("../http"),n.NodeHttpClient=n.util.inherit({handleRequest:function(t,r,i,o){var s=t.endpoint,a="";r||(r={});var u="https:"===s.protocol,f=e(u?"https":"http"),l={host:s.hostname,port:s.port,method:t.method,headers:t.headers,path:a+t.path};u&&!r.agent&&(l.agent=this.sslAgent()),n.util.update(l,r),delete l.proxy,delete l.timeout;var h=f.request(l,(function(e){i(e),e.emit("headers",e.statusCode,e.headers)}));return t.stream=h,h.setTimeout(r.timeout||0),h.once("timeout",(function(){var e="Connection timed out after "+r.timeout+"ms";o(n.util.error(new Error(e),{code:"TimeoutError"})),h.removeListener("error",o),h.on("error",(function(){})),h.abort()})),h.on("error",o),this.writeBody(h,t),h},writeBody:function(e,t){var r=t.body;r&&o&&s&&(r instanceof i||(r=this.bufferToStream(r)),r.pipe(this.progressStream(e,t))),r instanceof i?r.pipe(e):r?e.end(r):e.end()},sslAgent:function(){var t=e("https");return n.NodeHttpClient.sslAgent||(n.NodeHttpClient.sslAgent=new t.Agent({rejectUnauthorized:!0}),n.NodeHttpClient.sslAgent.setMaxListeners(0),Object.defineProperty(n.NodeHttpClient.sslAgent,"maxSockets",{enumerable:!0,get:function(){return t.globalAgent.maxSockets}})),n.NodeHttpClient.sslAgent},progressStream:function(e,t){var r=0,n=t.headers["Content-Length"],i=new o;return i._write=function(t,i,o){t&&(r+=t.length,e.emit("sendProgress",{loaded:r,total:n})),o()},i},bufferToStream:function(e){n.util.Buffer.isBuffer(e)||(e=new n.util.Buffer(e));var t=new s,r=0;return t._read=function(n){if(r>=e.length)return t.push(null);var i=r+n;i>e.length&&(i=e.length),t.push(e.slice(r,i)),r=i},t},emitter:null}),n.HttpClient.prototype=n.NodeHttpClient.prototype,n.HttpClient.streamsApiVersion=s?2:1},{"../core":6,"../http":9,http:70,https:44,stream:69}],11:[function(e,t,r){var n=e("../core"),i=e("events").EventEmitter;e("../http"),n.XHRClient=n.util.inherit({handleRequest:function(e,t,r,o){var s=this,a=e.endpoint,u=new i,f=a.protocol+"//"+a.hostname;80!=a.port&&443!=a.port&&(f+=":"+a.port),f+=e.path;var l=new XMLHttpRequest;return e.stream=l,t.timeout&&(l.timeout=t.timeout),l.addEventListener("readystatechange",(function(){try{if(0===l.status)return}catch(e){return}if(this.readyState===this.HEADERS_RECEIVED){try{l.responseType="arraybuffer"}catch(e){}u.statusCode=l.status,u.headers=s.parseHeaders(l.getAllResponseHeaders()),u.emit("headers",u.statusCode,u.headers)}else this.readyState===this.DONE&&s.finishRequest(l,u)}),!1),l.upload.addEventListener("progress",(function(e){u.emit("sendProgress",e)})),l.addEventListener("progress",(function(e){u.emit("receiveProgress",e)}),!1),l.addEventListener("timeout",(function(){o(n.util.error(new Error("Timeout"),{code:"TimeoutError"}))}),!1),l.addEventListener("error",(function(){o(n.util.error(new Error("Network Failure"),{code:"NetworkingError"}))}),!1),r(u),l.open(e.method,f,!0),n.util.each(e.headers,(function(e,t){"Content-Length"!==e&&"User-Agent"!==e&&"Host"!==e&&"Date"!==e&&l.setRequestHeader(e,t)})),e.body&&"object"===typeof e.body.buffer?l.send(e.body.buffer):l.send(e.body),u},parseHeaders:function(e){var t={};return n.util.arrayEach(e.split(/\r?\n/),(function(e){var r=e.split(":",1)[0],n=e.substring(r.length+2);r.length>0&&(t[r]=n)})),t},finishRequest:function(e,t){var r;if("arraybuffer"===e.responseType&&e.response){var i=e.response;r=new n.util.Buffer(i.byteLength);for(var o=new Uint8Array(i),s=0;s<r.length;++s)r[s]=o[s]}try{r||"string"!==typeof e.responseText||(r=new n.util.Buffer(e.responseText))}catch(a){}r&&t.emit("data",r),t.emit("end")}}),n.HttpClient.prototype=n.XHRClient.prototype,n.HttpClient.streamsApiVersion=1},{"../core":6,"../http":9,events:43}],12:[function(e,t,r){var n=e("./core"),i=e("int64-buffer");n.Long={fromNumber:function(e){return this.int64=new i.Int64LE(e),this.int64},fromString:function(e){return this.int64=new i.Int64LE(e,10),this.int64},toBuffer:function(){return this.int64.toBuffer()},toNumber:function(){return this.int64.toNumber()}}},{"./core":6,"int64-buffer":33}],13:[function(e,t,r){var n=e("./core");n.util.inherit;n.rowExistenceExpectation={IGNORE:"IGNORE",EXPECT_EXIST:"EXPECT_EXIST",EXPECT_NOT_EXIST:"EXPECT_NOT_EXIST"},n.Direction={FORWARD:"FORWARD",BACKWARD:"BACKWARD"},n.UpdateType={PUT:"PUT",DELETE:"DELETE",DELETE_ALL:"DELETE_ALL"},n.ReturnType={NONE:0,Primarykey:1},n.INF_MIN={},n.INF_MAX={},n.PK_AUTO_INCR={}},{"./core":6}],14:[function(e,t,r){var n=e("../core"),i=e("./tablestore_proto.js").tablestore.proto;e("./tablestore_filter_proto.js").tablestore.filter.proto;n.decoder={decode:function(e,t){return o[e](t)},decodeCreateTable:function(e){var t=i.CreateTableResponse.decode(e);return t},decodeListTable:function(e){var t=i.ListTableResponse.decode(e);return t},decodeDeleteTable:function(e){var t=i.DeleteTableResponse.decode(e);return t},decodeUpdateTable:function(e){var t=i.UpdateTableResponse.decode(e);return t},decodeDescribeTable:function(e){var t=i.DescribeTableResponse.decode(e);return t},decodeGetRow:function(e){var t=i.GetRowResponse.decode(e);if(t.row&&t.row.limit>t.row.offset){var r=new n.PlainBufferInputStream(t.row),o=new n.PlainBufferCodedInputStream(r);t.row=o.readRow()}else t.row={};return t},decodePutRow:function(e){var t=i.UpdateRowResponse.decode(e);if(t.row&&t.row.limit>t.row.offset){var r=new n.PlainBufferInputStream(t.row),o=new n.PlainBufferCodedInputStream(r);t.row=o.readRow()}else t.row={};return t},decodeUpdateRow:function(e){var t=i.UpdateRowResponse.decode(e);return t},decodeDeleteRow:function(e){var t=i.DeleteRowResponse.decode(e);return t},decodeGetRange:function(e){var t=i.GetRangeResponse.decode(e);if(t.rows&&t.rows.limit>t.rows.offset){var r=new n.PlainBufferInputStream(t.rows),o=new n.PlainBufferCodedInputStream(r);t.rows=o.readRows()}else t.rows=[];if(null!=t.next_start_primary_key){var s=new n.PlainBufferInputStream(t.next_start_primary_key),a=new n.PlainBufferCodedInputStream(s),u=a.readRow();t.next_start_primary_key=u.primaryKey}return t},decodeBatchGetRow:function(e){var t=i.BatchGetRowResponse.decode(e);return t.tables=n.decoder._parseBatchGetRow(t.tables),t},_parseBatchGetRow:function(e){var t=[];for(var r in e)t.push(n.decoder._parseGetRowItem(e[r].rows,e[r].table_name));return t},_parseGetRowItem:function(e,t){var r=[];for(var i in e){var o=null,s=null,a=null,u=null;if(e[i].is_ok){if(e[i].row&&0!=e[i].row.length){var f=new n.PlainBufferInputStream(e[i].row),l=new n.PlainBufferCodedInputStream(f),h=l.readRow();o=h.primaryKey,s=h.attributes}}else a=e[i].error.code,u=e[i].error.message;var c={isOk:e[i].is_ok,errorCode:a,errorMessage:u,tableName:t,capacityUnit:null==e[i].consumed?"":e[i].consumed.capacity_unit,primaryKey:o,attributes:s};r.push(c)}return r},decodeBatchWriteRow:function(e){var t=i.BatchWriteRowResponse.decode(e);return t.tables=n.decoder._parseBatchWriteRow(t.tables),t},_parseBatchWriteRow:function(e){var t=[];for(var r in e){var i=e[r].table_name;for(var o in t[i]=[],e[r].rows){o=n.decoder._parseWriteRowItem(e[r].rows[o]);t[i].push(o)}}return t},_parseWriteRowItem:function(e){var t=null,r=null,i=null;if(e.is_ok){if(null!=e.row&&0!=e.row.length){var o=new n.PlainBufferInputStream(e.row),s=new n.PlainBufferCodedInputStream(o),a=s.readRow();t=a.primaryKey}}else r=e.error.code,i=e.error.message;var u={isOk:e.is_ok,errorCode:r,errorMessage:i,capacityUnit:null==e.consumed?"":e.consumed.capacity_unit,primaryKey:t};return u}};var o={createTable:n.decoder.decodeCreateTable,listTable:n.decoder.decodeListTable,deleteTable:n.decoder.decodeDeleteTable,updateTable:n.decoder.decodeUpdateTable,describeTable:n.decoder.decodeDescribeTable,getRow:n.decoder.decodeGetRow,putRow:n.decoder.decodePutRow,updateRow:n.decoder.decodeUpdateRow,deleteRow:n.decoder.decodeDeleteRow,getRange:n.decoder.decodeGetRange,batchGetRow:n.decoder.decodeBatchGetRow,batchWriteRow:n.decoder.decodeBatchWriteRow}},{"../core":6,"./tablestore_filter_proto.js":21,"./tablestore_proto.js":22}],15:[function(e,t,r){var n=e("../core"),i=e("./tablestore_proto.js").tablestore.proto,o=e("./tablestore_filter_proto.js").tablestore.filter.proto;COLUMN_CONDITION_TYPE_MAP={COMPOSITE_COLUMN_CONDITION:o.FT_COMPOSITE_COLUMN_VALUE,SINGLE_COLUMN_CONDITION:o.FT_SINGLE_COLUMN_VALUE},n.encoder={encode:function(e,t){return s[e](t)},encodeCreateTable:function(e){var t=new i.CreateTableRequest;return t.reserved_throughput={capacity_unit:e.reservedThroughput.capacityUnit},t.table_meta={table_name:e.tableMeta.tableName,primary_key:e.tableMeta.primaryKey},e.tableOptions&&(t.table_options={time_to_live:e.tableOptions.timeToLive,max_versions:e.tableOptions.maxVersions},e.tableOptions.maxTimeDeviation&&(t.table_options.deviation_cell_version_in_sec=e.tableOptions.maxTimeDeviation)),t},encodeListTable:function(e){var t=new i.ListTableRequest;return t},encodeDeleteTable:function(e){var t=new i.DeleteTableRequest;return t.table_name=e.tableName,t},encodeUpdateTable:function(e){var t=new i.UpdateTableRequest;return t.table_name=e.tableName,t.reserved_throughput={capacity_unit:e.reservedThroughput.capacityUnit},t.table_options={time_to_live:e.tableOptions.timeToLive,max_versions:e.tableOptions.maxVersions,deviation_cell_version_in_sec:e.tableOptions.maxTimeDeviation},t},encodeDescribeTable:function(e){var t=new i.DescribeTableRequest;return t.table_name=e.tableName,t},encodeGetRow:function(e){var t=new i.GetRowRequest({table_name:e.tableName,primary_key:n.PlainBufferBuilder.serializePrimaryKey(e.primaryKey),attribute_columns:e.attributeColumns});if(e.maxVersions?t.max_versions=e.maxVersions:t.max_versions=1,e.columnFilter){var r=new o.Filter;n.encoder._makeColumnCondition(r,e.columnFilter),t.filter=r.encode()}return e.timeRange&&(t.time_range={start_time:e.timeRange.startTime,end_time:e.timeRange.endTime,specific_time:e.timeRange.specificTime}),e.startColumn&&(t.start_column=e.startColumn),e.endColumn&&(t.end_column=e.endColumn),e.columnsToGet&&(t.columns_to_get=e.columnsToGet),t},encodePutRow:function(e){var t=new i.PutRowRequest;return t.table_name=e.tableName,t.row=n.PlainBufferBuilder.serializeForPutRow(e.primaryKey,e.attributeColumns),void 0!==e.condition&&null!==e.condition||(e.condtion=new n.Condition(n.RowExistenceExpectation.IGNORE,null)),t.condition={},n.encoder._makeCondition(t.condition,e.condition),e.returnContent&&e.returnContent.returnType&&(t.return_content={return_type:e.returnContent.returnType}),t},encodeUpdateRow:function(e){var t=new i.UpdateRowRequest;return t.table_name=e.tableName,t.row_change=n.PlainBufferBuilder.serializeForUpdateRow(e.primaryKey,e.updateOfAttributeColumns),void 0!==e.condition&&null!==e.condition||(e.condtion=new n.Condition(n.RowExistenceExpectation.IGNORE,null)),t.condition={},n.encoder._makeCondition(t.condition,e.condition),e.returnContent&&e.returnContent.returnType&&(t.return_content={return_type:e.returnContent.returnType}),t},encodeDeleteRow:function(e){var t=new i.DeleteRowRequest;return t.table_name=e.tableName,t.primary_key=n.PlainBufferBuilder.serializeForDeleteRow(e.primaryKey),void 0!==e.condition&&null!==e.condition||(e.condtion=new n.Condition(n.RowExistenceExpectation.IGNORE,null)),t.condition={},n.encoder._makeCondition(t.condition,e.condition),e.returnContent&&e.returnContent.returnType&&(t.return_content={return_type:e.returnContent.returnType}),t},encodeGetRange:function(e){var t=new i.GetRangeRequest({table_name:e.tableName,inclusive_start_primary_key:n.PlainBufferBuilder.serializePrimaryKey(e.inclusiveStartPrimaryKey),exclusive_end_primary_key:n.PlainBufferBuilder.serializePrimaryKey(e.exclusiveEndPrimaryKey),direction:e.direction});if(e.maxVersions?t.max_versions=e.maxVersions:t.max_versions=1,e.columnFilter){var r=new o.Filter;n.encoder._makeColumnCondition(r,e.columnFilter),t.filter=r.encode()}return e.limit&&(t.limit=e.limit),e.timeRange&&(t.time_range={start_time:e.timeRange.startTime,end_time:e.timeRange.endTime,specific_time:e.timeRange.specificTime}),e.startColumn&&(t.start_column=e.startColumn),e.endColumn&&(t.end_column=e.endColumn),e.columnsToGet&&(t.columns_to_get=e.columnsToGet),t},encodeBatchGetRow:function(e){if(void 0===e.tables||null===e.tables)throw new Error("params.tables is not correct");for(var t=new i.BatchGetRowRequest,r=0;r<e.tables.length;r++){var s={table_name:e.tables[r].tableName,primary_key:[]};for(var a in e.tables[r].primaryKey)s.primary_key.push(n.PlainBufferBuilder.serializePrimaryKey(e.tables[r].primaryKey[a]));if(e.tables[r].columnFilter){var u=new o.Filter;n.encoder._makeColumnCondition(u,e.tables[r].columnFilter),s.filter=u.encode()}e.tables[r].maxVersions?s.max_versions=e.tables[r].maxVersions:s.max_versions=1,e.tables[r].timeRange&&(s.time_range={start_time:e.tables[r].timeRange.startTime,end_time:e.tables[r].timeRange.endTime,specific_time:e.tables[r].timeRange.specificTime}),e.tables[r].startColumn&&(s.start_column=e.tables[r].startColumn),e.tables[r].endColumn&&(s.end_column=e.tables[r].endColumn),e.tables[r].columnsToGet&&(s.columns_to_get=e.tables[r].columnsToGet),t.tables.push(s)}return t},encodeBatchWriteRow:function(e){if(void 0===e.tables||null===e.tables||e.tables.length<=0)throw new Error("params.tables is not correct");for(var t=new i.BatchWriteRowRequest,r=0;r<e.tables.length;r++){var o={table_name:e.tables[r].tableName,rows:[]};for(var s in e.tables[r].rows){var a={};switch(a.type=e.tables[r].rows[s].type,e.tables[r].rows[s].type){case"PUT":a.row_change=n.PlainBufferBuilder.serializeForPutRow(e.tables[r].rows[s].primaryKey,e.tables[r].rows[s].attributeColumns);break;case"UPDATE":a.row_change=n.PlainBufferBuilder.serializeForUpdateRow(e.tables[r].rows[s].primaryKey,e.tables[r].rows[s].attributeColumns);break;case"DELETE":a.row_change=n.PlainBufferBuilder.serializeForDeleteRow(e.tables[r].rows[s].primaryKey,e.tables[r].rows[s].attributeColumns);break;default:throw new Error("batchwriterow type is error:"+e.tables[r].rows[s].type)}e.tables[r].rows[s].condition&&(a.condition={},n.encoder._makeCondition(a.condition,e.tables[r].rows[s].condition)),e.tables[r].rows[s].returnContent&&e.tables[r].rows[s].returnContent.returnType&&(a.return_content={return_type:e.tables[r].rows[s].returnContent.returnType}),o.rows.push(a)}t.tables.push(o)}return t},_makeColumnCondition:function(e,t){if(null!==t){if(!t instanceof n.ColumnCondition)throw new Error("column condition should be an instance of ColumnCondition");if(e.type=t.getType(),null==e.type)throw new Error("column_condition_type should be one of TableStore.ColumnConditionType");if(t instanceof n.CompositeCondition)e.filter=n.encoder._makeCompositeCondition(t);else if(t instanceof n.SingleColumnCondition)e.filter=n.encoder._makeSingleColumnCondition(t);else{if(!(t instanceof n.ColumnPaginationFilter))throw new Error("expect CompositeCondition, RelationCondition, ColumnPaginationFilter but not ");e.filter=n.encoder._makeColumnPaginationFilter(t)}}},_makeCompositeCondition:function(e){var t=new o.CompositeColumnValueFilter;if(t.combinator=e.combinator,void 0===t.combinator||null===t.combinator)throw new Error("LogicalOperator should be one of TableStore.LogicalOperator");for(sub in e.sub_conditions){var r={};n.encoder._makeColumnCondition(r,e.sub_conditions[sub]),t.sub_filters.push(r)}return t.toBuffer()},_makeSingleColumnCondition:function(e){var t=new o.SingleColumnValueFilter;if(t.comparator=e.comparator,null===t.comparator)throw new Error("ComparatorType should be one of [%s], not %s");return t.column_name=e.columnName,t.column_value=n.PlainBufferBuilder.serializeColumnValue(e.columnValue),t.filter_if_missing=!e.passIfMissing,t.latest_version_only=e.latestVersionOnly,t.toBuffer()},_makeColumnPaginationFilter:function(e){var t=new o.ColumnPaginationFilter;return t.offset=e.offset,t.limit=e.limit,t.toBuffer()},_makeCondition:function(e,t){if(!t instanceof n.Condition)throw new Error("condition should be an instance of TableStore.Condition");if(e.row_existence=t.rowExistenceExpectation,void 0===e.row_existence||null===e.row_existence)throw new Error("rowExistenceExpectation should be one of TableStore.RowExistenceExpectation");if(null!=t.columnCondition){var r=new o.Filter;n.encoder._makeColumnCondition(r,t.columnCondition),e.column_condition=r.encode()}}};var s={createTable:n.encoder.encodeCreateTable,listTable:n.encoder.encodeListTable,deleteTable:n.encoder.encodeDeleteTable,updateTable:n.encoder.encodeUpdateTable,describeTable:n.encoder.encodeDescribeTable,getRow:n.encoder.encodeGetRow,putRow:n.encoder.encodePutRow,updateRow:n.encoder.encodeUpdateRow,deleteRow:n.encoder.encodeDeleteRow,getRange:n.encoder.encodeGetRange,batchGetRow:n.encoder.encodeBatchGetRow,batchWriteRow:n.encoder.encodeBatchWriteRow}},{"../core":6,"./tablestore_filter_proto.js":21,"./tablestore_proto.js":22}],16:[function(e,t,r){(function(t){var r=e("../core"),n=e("int64-buffer"),i=r.util.inherit;r.PlainBufferCodedInputStream=i({constructor:function(e){this.inputStream=e},readTag:function(){return this.inputStream.readTag()},checkLastTagWas:function(e){return this.inputStream.checkLastTagWas(e)},getLastTag:function(){return this.inputStream.getLastTag()},readHeader:function(){return this.inputStream.readInt32()},readPrimaryKeyValue:function(e){if(!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL_VALUE))throw new Error("Expect TAG_CELL_VALUE but it was"+this.getLastTag());this.inputStream.readRawLittleEndian32();var t=this.inputStream.readRawByte(),n=void 0;if(t===r.plainBufferConsts.VT_INTEGER){var i=this.inputStream.readInt64();e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_INTEGER),e=r.plainBufferCrc8.crcInt64Buf(e,i.toBuffer()),this.readTag(),n=i}else if(t===r.plainBufferConsts.VT_STRING){var o=this.inputStream.readInt32(),s=this.inputStream.readUtfString(o);e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_STRING),e=r.plainBufferCrc8.crcInt32(e,o),e=r.plainBufferCrc8.crcString(e,s),this.readTag(),n=s}else{if(t!==r.plainBufferConsts.VT_BLOB)throw new Error("Unsupported primary key type"+t);o=this.inputStream.readInt32();var a=this.inputStream.readBytes(o);e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_BLOB),e=r.plainBufferCrc8.crcInt32(e,o),e=r.plainBufferCrc8.crcString(e,a),this.readTag(),n=a}return{pkVal:n,cellCheckSum:e}},readColumnValue:function(e){if(!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL_VALUE))throw new Error("Expect TAG_CELL_VALUE but it was"+this.getLastTag());var t=void 0;this.inputStream.readRawLittleEndian32();var n=this.inputStream.readRawByte();if(n==r.plainBufferConsts.VT_INTEGER){var i=this.inputStream.readInt64();e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_INTEGER),e=r.plainBufferCrc8.crcInt64Buf(e,i.toBuffer()),this.readTag(),t=i}else if(n==r.plainBufferConsts.VT_STRING){var o=this.inputStream.readInt32(),s=this.inputStream.readUtfString(o);e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_STRING),e=r.plainBufferCrc8.crcInt32(e,o),e=r.plainBufferCrc8.crcString(e,s),this.readTag(),t=s}else if(n==r.plainBufferConsts.VT_BLOB){o=this.inputStream.readInt32();var a=this.inputStream.readBytes(o);e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_BLOB),e=r.plainBufferCrc8.crcInt32(e,o),e=r.plainBufferCrc8.crcString(e,a),this.readTag(),t=a}else if(n==r.plainBufferConsts.VT_BOOLEAN){var u=this.inputStream.readBoolean(),f=(e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_BOOLEAN),1==u);e=r.plainBufferCrc8.crcInt8(e,f),this.readTag(),t=u}else{if(n!=r.plainBufferConsts.VT_DOUBLE)throw new Error("Unsupported column type: "+n);var l=this.inputStream.readDoubleAndInt64(),h=(i=l.int64LE,l.doubleVal);e=r.plainBufferCrc8.crcInt8(e,r.plainBufferConsts.VT_DOUBLE),e=r.plainBufferCrc8.crcInt64Buf(e,i.toBuffer()),this.readTag(),t=h}return{columnVal:t,cellCheckSum:e}},readPrimaryKeyColumn:function(e){if(!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL))throw new Error("Expect TAG_CELL but it was "+this.getLastTag());if(this.readTag(),!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL_NAME))throw new Error("Expect TAG_CELL_NAME but it was "+this.getLastTag());var t,n=0,i=this.inputStream.readRawLittleEndian32(),o=this.inputStream.readUtfString(i);n=r.plainBufferCrc8.crcString(n,o);if(this.readTag(),!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL_VALUE))throw new Error("Expect TAG_CELL_VALUE but it was "+this.getLastTag());var s=this.readPrimaryKeyValue(n);if(t=s.pkVal,n=s.cellCheckSum,this.getLastTag()!=r.plainBufferConsts.TAG_CELL_CHECKSUM)throw new Error("Expect TAG_CELL_CHECKSUM but it was "+this.getLastTag());var a=this.inputStream.readRawByte();if(a!=n)throw new Error("Checksum mismatch. expected:"+a+",actual:"+n);this.readTag();e=r.plainBufferCrc8.crcInt8(e,n);return{columnName:o,primaryKeyValue:t,rowCheckSum:e}},readColumn:function(e){if(!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL))throw new Error("Expect TAG_CELL but it was "+this.getLastTag());if(this.readTag(),!this.checkLastTagWas(r.plainBufferConsts.TAG_CELL_NAME))throw new Error("Expect TAG_CELL_NAME but it was "+this.getLastTag());var t=0,n=null,i=null,o=null,s=this.inputStream.readRawLittleEndian32();n=this.inputStream.readUtfString(s),t=r.plainBufferCrc8.crcString(t,n);if(this.readTag(),this.getLastTag()==r.plainBufferConsts.TAG_CELL_VALUE){var a=this.readColumnValue(t);i=a.columnVal,t=a.cellCheckSum}if(this.getLastTag()==r.plainBufferConsts.TAG_CELL_TYPE&&(cell_type=r.plainBufferCrc8.crcInt8(t,cell_type),this.readTag()),this.getLastTag()==r.plainBufferConsts.TAG_CELL_TIMESTAMP){var u=this.inputStream.readInt64();o=u,t=r.plainBufferCrc8.crcInt64Buf(t,u.toBuffer()),this.readTag()}if(this.getLastTag()!=r.plainBufferConsts.TAG_CELL_CHECKSUM)throw new Error("Expect TAG_CELL_CHECKSUM but it was "+this.getLastTag());var f=this.inputStream.readRawByte();if(f!=t)throw new Error("Checksum mismatch.");this.readTag();e=r.plainBufferCrc8.crcInt8(e,t);return{columnName:n,columnValue:i,timestamp:o,rowCheckSum:e}},readRowWithoutHeader:function(){var e=0,t=[],n=[];if(!this.checkLastTagWas(r.plainBufferConsts.TAG_ROW_PK))throw new Error("Expect TAG_ROW_PK but it was "+this.getLastTag());this.readTag();while(this.checkLastTagWas(r.plainBufferConsts.TAG_CELL)){var i=this.readPrimaryKeyColumn(e);t.push({name:i.columnName,value:i.primaryKeyValue}),e=i.rowCheckSum}if(this.checkLastTagWas(r.plainBufferConsts.TAG_ROW_DATA)){this.readTag();while(this.checkLastTagWas(r.plainBufferConsts.TAG_CELL)){var o=this.readColumn(e);n.push({columnName:o.columnName,columnValue:o.columnValue,timestamp:o.timestamp}),e=o.rowCheckSum}}if(this.checkLastTagWas(r.plainBufferConsts.TAG_DELETE_ROW_MARKER)?(this.readTag(),e=r.plainBufferCrc8.crcInt8(e,1)):e=r.plainBufferCrc8.crcInt8(e,0),!this.checkLastTagWas(r.plainBufferConsts.TAG_ROW_CHECKSUM))throw new Error("Expect TAG_ROW_CHECKSUM but it was "+this.getLastTag());var s=this.inputStream.readRawByte();if(s!=e)throw new Error("Checksum is mismatch.");return this.readTag(),{primaryKey:t,attributes:n}},readRow:function(){if(this.readHeader()!=r.plainBufferConsts.HEADER)throw new Error("Invalid header from plain buffer.");return this.readTag(),this.readRowWithoutHeader()},readRows:function(){if(this.readHeader()!=r.plainBufferConsts.HEADER)throw new Error("Invalid header from plain buffer.");this.readTag();var e=[];while(!this.inputStream.isAtEnd()){var t=this.readRowWithoutHeader();e.push({primaryKey:t.primaryKey,attributes:t.attributes})}return e}}),r.PlainBufferCodedOutputStream=i({constructor:function(e){this.outputStream=e},writeHeader:function(){this.outputStream.writeRawLittleEndian32(r.plainBufferConsts.HEADER)},writeTag:function(e){this.outputStream.writeRawByte(e)},writeCellName:function(e,t){return this.writeTag(r.plainBufferConsts.TAG_CELL_NAME),this.outputStream.writeRawLittleEndian32(e.length),this.outputStream.writeBytes(e),t=r.plainBufferCrc8.crcString(t,e),t},writePrimaryKeyValue:function(e,i){if(this.writeTag(r.plainBufferConsts.TAG_CELL_VALUE),e===r.INF_MIN)this.outputStream.writeRawLittleEndian32(1),this.outputStream.writeRawByte(r.plainBufferConsts.VT_INF_MIN),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_INF_MIN);else if(e===r.INF_MAX)this.outputStream.writeRawLittleEndian32(1),this.outputStream.writeRawByte(r.plainBufferConsts.VT_INF_MAX),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_INF_MAX);else if(e===r.PK_AUTO_INCR)this.outputStream.writeRawLittleEndian32(1),this.outputStream.writeRawByte(r.plainBufferConsts.VT_AUTO_INCREMENT),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_AUTO_INCREMENT);else if(e instanceof n.Int64LE)this.outputStream.writeRawLittleEndian32(1+r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE),this.outputStream.writeRawByte(r.plainBufferConsts.VT_INTEGER),this.outputStream.writeInt64LE(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_INTEGER),i=r.plainBufferCrc8.crcInt64Buf(i,e.toBuffer());else if("string"===typeof e){var o=e,s=r.util.string.byteLength(o),a=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1;this.outputStream.writeRawLittleEndian32(a+s),this.outputStream.writeRawByte(r.plainBufferConsts.VT_STRING),this.outputStream.writeRawLittleEndian32(s),this.outputStream.writeBytes(o),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_STRING),i=r.plainBufferCrc8.crcInt32(i,s),i=r.plainBufferCrc8.crcString(i,o)}else{if(!(e instanceof t))throw new Error("Unsupported primary key type: "+typeof e);var u=e;a=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1;this.outputStream.writeRawLittleEndian32(a+u.length),this.outputStream.writeRawByte(r.plainBufferConsts.VT_BLOB),this.outputStream.writeRawLittleEndian32(u.length),this.outputStream.writeBytes(u),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_BLOB),i=r.plainBufferCrc8.crcInt32(i,u.length),i=r.plainBufferCrc8.crcString(i,u.toString())}return i},writeColumnValueWithChecksum:function(e,i){if(this.writeTag(r.plainBufferConsts.TAG_CELL_VALUE),e instanceof n.Int64LE)this.outputStream.writeRawLittleEndian32(1+r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE),this.outputStream.writeRawByte(r.plainBufferConsts.VT_INTEGER),this.outputStream.writeInt64LE(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_INTEGER),i=r.plainBufferCrc8.crcInt64Buf(i,e.toBuffer());else if("string"===typeof e){var o=r.util.string.byteLength(e),s=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1;this.outputStream.writeRawLittleEndian32(s+o),this.outputStream.writeRawByte(r.plainBufferConsts.VT_STRING),this.outputStream.writeRawLittleEndian32(o),this.outputStream.writeBytes(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_STRING),i=r.plainBufferCrc8.crcInt32(i,o),i=r.plainBufferCrc8.crcString(i,e)}else if(e instanceof t)s=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1,this.outputStream.writeRawLittleEndian32(s+e.length),this.outputStream.writeRawByte(r.plainBufferConsts.VT_BLOB),this.outputStream.writeRawLittleEndian32(e.length),this.outputStream.writeBytes(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_BLOB),i=r.plainBufferCrc8.crcInt32(i,e.length),i=r.plainBufferCrc8.crcString(i,e);else if("boolean"===typeof e)this.outputStream.writeRawLittleEndian32(2),this.outputStream.writeRawByte(r.plainBufferConsts.VT_BOOLEAN),this.outputStream.writeBoolean(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_BOOLEAN),i=e?r.plainBufferCrc8.crcInt8(i,1):r.plainBufferCrc8.crcInt8(i,0);else{if("number"!==typeof e)throw new Error("Unsupported column type "+typeof e);var a=r.util.Int64.doubleToRawLongBits(e);this.outputStream.writeRawLittleEndian32(1+r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE),this.outputStream.writeRawByte(r.plainBufferConsts.VT_DOUBLE),this.outputStream.writeDouble(e),i=r.plainBufferCrc8.crcInt8(i,r.plainBufferConsts.VT_DOUBLE),i=r.plainBufferCrc8.crcInt64Buf(i,a.toBuffer())}return i},writeColumnValue:function(e){if(e instanceof n.Int64LE)this.outputStream.writeRawByte(r.plainBufferConsts.VT_INTEGER),this.outputStream.writeInt64LE(e);else if("string"===typeof e)this.outputStream.writeRawByte(r.plainBufferConsts.VT_STRING),this.outputStream.writeRawLittleEndian32(r.util.string.byteLength(e)),this.outputStream.writeBytes(e);else if(e instanceof t)this.outputStream.writeRawByte(r.plainBufferConsts.VT_BLOB),this.outputStream.writeRawLittleEndian32(e.length),this.outputStream.writeBytes(e);else if("boolean"===typeof e)this.outputStream.writeRawByte(r.plainBufferConsts.VT_BOOLEAN),this.outputStream.writeBoolean(e);else{if("number"!==typeof e)throw new Error("Unsupported column type: "+typeof e);this.outputStream.writeRawByte(r.plainBufferConsts.VT_DOUBLE),this.outputStream.writeDouble(e)}},writePrimaryKeyColumn:function(e,t,n){var i=0;return this.writeTag(r.plainBufferConsts.TAG_CELL),i=this.writeCellName(e,i),i=this.writePrimaryKeyValue(t,i),this.writeTag(r.plainBufferConsts.TAG_CELL_CHECKSUM),this.outputStream.writeRawByte(i),n=r.plainBufferCrc8.crcInt8(n,i),n},writeColumn:function(e,t,n,i){var o=0;if(this.writeTag(r.plainBufferConsts.TAG_CELL),o=this.writeCellName(e,o),o=this.writeColumnValueWithChecksum(t,o),void 0!==n&&null!==n){this.writeTag(r.plainBufferConsts.TAG_CELL_TIMESTAMP);var s=r.Long.fromNumber(n);this.outputStream.writeInt64LE(s),o=r.plainBufferCrc8.crcInt64Buf(o,s.toBuffer())}return this.writeTag(r.plainBufferConsts.TAG_CELL_CHECKSUM),this.outputStream.writeRawByte(o),i=r.plainBufferCrc8.crcInt8(i,o),i},writeUpdateColumn:function(e,t,n,i){var o=0;this.writeTag(r.plainBufferConsts.TAG_CELL);o=this.writeCellName(t,o);var s=null;if(null!=n&&(n instanceof Array?(null!=n[0]&&(o=this.writeColumnValueWithChecksum(n[0],o)),null!=n[1]&&(s=n[1])):o=this.writeColumnValueWithChecksum(n,o)),e===r.UpdateType.DELETE?(this.writeTag(r.plainBufferConsts.TAG_CELL_TYPE),this.outputStream.writeRawByte(r.plainBufferConsts.DELETE_ONE_VERSION)):e==r.UpdateType.DELETE_ALL&&(this.writeTag(r.plainBufferConsts.TAG_CELL_TYPE),this.outputStream.writeRawByte(r.plainBufferConsts.DELETE_ALL_VERSION)),null!=s){this.writeTag(r.plainBufferConsts.TAG_CELL_TIMESTAMP);var a=r.Long.fromNumber(s);this.outputStream.writeInt64LE(a)}if(null!=s){a=r.Long.fromNumber(s);o=r.plainBufferCrc8.crcInt64Buf(o,a.toBuffer())}return e===r.UpdateType.DELETE&&(o=r.plainBufferCrc8.crcInt8(o,r.plainBufferConsts.DELETE_ONE_VERSION)),e===r.UpdateType.DELETE_ALL&&(o=r.plainBufferCrc8.crcInt8(o,r.plainBufferConsts.DELETE_ALL_VERSION)),this.writeTag(r.plainBufferConsts.TAG_CELL_CHECKSUM),this.outputStream.writeRawByte(o),i=r.plainBufferCrc8.crcInt8(i,o),i},writePrimaryKey:function(e,t){this.writeTag(r.plainBufferConsts.TAG_ROW_PK);for(var n=0;n<e.length;n++)for(var i in e[n])t=this.writePrimaryKeyColumn(i,e[n][i],t);return t},writeColumns:function(e,t){if(null!=e&&0!=e.length){this.writeTag(r.plainBufferConsts.TAG_ROW_DATA);for(var n=0;n<e.length;n++)for(var i in e[n]){t=this.writeColumn(i,e[n][i],e[n].timestamp,t);break}}return t},writeUpdateColumns:function(e,t){if(0==e.length)return t;this.writeTag(r.plainBufferConsts.TAG_ROW_DATA);for(var n=0;n<e.length;n++)for(var i in e[n]){var o=e[n][i];for(var s in o)if(r.UpdateType.DELETE_ALL===i)t=this.writeUpdateColumn(i,o[s],null,t);else if(r.UpdateType.DELETE===i)for(var a in o[s])t=this.writeUpdateColumn(i,a,[null,o[s][a]],t);else if(r.UpdateType.PUT===i)if(void 0!==o[s].timestamp)for(var a in o[s]){t=this.writeUpdateColumn(i,a,[o[s][a],o[s].timestamp],t);break}else for(var a in o[s]){t=this.writeUpdateColumn(i,a,o[s][a],t);break}}return t},writeDeleteMarker:function(e){return this.writeTag(r.plainBufferConsts.TAG_DELETE_ROW_MARKER),r.plainBufferCrc8.crcInt8(e,1)},writeRowChecksum:function(e){this.writeTag(r.plainBufferConsts.TAG_ROW_CHECKSUM),this.outputStream.writeRawByte(e)}})}).call(this,e("buffer").Buffer)},{"../core":6,buffer:40,"int64-buffer":33}],17:[function(e,t,r){var n=e("../core");n.plainBufferConsts={HEADER:117,TAG_ROW_PK:1,TAG_ROW_DATA:2,TAG_CELL:3,TAG_CELL_NAME:4,TAG_CELL_VALUE:5,TAG_CELL_TYPE:6,TAG_CELL_TIMESTAMP:7,TAG_DELETE_ROW_MARKER:8,TAG_ROW_CHECKSUM:9,TAG_CELL_CHECKSUM:10,TAG_EXTENSION:11,TAG_SEQ_INFO:12,TAG_SEQ_INFO_EPOCH:13,TAG_SEQ_INFO_TS:14,TAG_SEQ_INFO_ROW_INDEX:15,DELETE_ALL_VERSION:1,DELETE_ONE_VERSION:3,VT_INTEGER:0,VT_DOUBLE:1,VT_BOOLEAN:2,VT_STRING:3,VT_NULL:6,VT_BLOB:7,VT_INF_MIN:9,VT_INF_MAX:10,VT_AUTO_INCREMENT:11,LITTLE_ENDIAN_32_SIZE:4,LITTLE_ENDIAN_64_SIZE:8,MAX_BUFFER_SIZE:67108864}},{"../core":6}],18:[function(e,t,r){var n=e("../core"),i=n.util.Buffer,o=[0,7,14,9,28,27,18,21,56,63,54,49,36,35,42,45,112,119,126,121,108,107,98,101,72,79,70,65,84,83,90,93,224,231,238,233,252,251,242,245,216,223,214,209,196,195,202,205,144,151,158,153,140,139,130,133,168,175,166,161,180,179,186,189,199,192,201,206,219,220,213,210,255,248,241,246,227,228,237,234,183,176,185,190,171,172,165,162,143,136,129,134,147,148,157,154,39,32,41,46,59,60,53,50,31,24,17,22,3,4,13,10,87,80,89,94,75,76,69,66,111,104,97,102,115,116,125,122,137,142,135,128,149,146,155,156,177,182,191,184,173,170,163,164,249,254,247,240,229,226,235,236,193,198,207,200,221,218,211,212,105,110,103,96,117,114,123,124,81,86,95,88,77,74,67,68,25,30,23,16,5,2,11,12,33,38,47,40,61,58,51,52,78,73,64,71,82,85,92,91,118,113,120,127,106,109,100,99,62,57,48,55,34,37,44,43,6,1,8,15,26,29,20,19,174,169,160,167,178,181,188,187,150,145,152,159,138,141,132,131,222,217,208,215,194,197,204,203,230,225,232,239,250,253,244,243];n.plainBufferCrc8={update:function(e,t){return n.plainBufferCrc8._update(e,t)},_update:function(e,t){for(var r=new i(t),n=0;n<r.length;n++)e=o[255&e^r[n]];return e},crcString:function(e,t){return this.update(e,t)},crcInt8:function(e,t){return o[255&e^t]},crcInt32:function(e,t){for(var r=0;r<4;r++)e=this.crcInt8(e,t>>8*r&255);return e},crcInt64Buf:function(e,t){return e=this.crcInt8(e,255&t[0]),e=this.crcInt8(e,255&t[1]),e=this.crcInt8(e,255&t[2]),e=this.crcInt8(e,255&t[3]),e=this.crcInt8(e,255&t[4]),e=this.crcInt8(e,255&t[5]),e=this.crcInt8(e,255&t[6]),e=this.crcInt8(e,255&t[7]),e}}},{"../core":6}],19:[function(e,t,r){var n=e("../core"),i=e("int64-buffer"),o=n.util.Buffer,s=n.util.inherit;n.PlainBufferInputStream=s({constructor:function(e){n.util.Buffer.isBuffer(e.buffer)?this.buffer=e.buffer:this.buffer=new n.util.Buffer(e.buffer),this.bufferLimit=e.limit,this.curPos=e.offset,this.lastTag=0},isAtEnd:function(){return this.bufferLimit===this.curPos},readTag:function(){return this.isAtEnd()?(this.lastTag=0,0):(this.lastTag=this.readRawByte(),this.lastTag)},checkLastTagWas:function(e){return this.lastTag===e},getLastTag:function(){return this.lastTag},readRawByte:function(){if(this.isAtEnd())throw new Error("Read raw byte encountered EOF.");var e=this.curPos;return this.curPos+=1,this.buffer[e]},readRawLittleEndian64:function(){var e=this.readRawByte(),t=this.readRawByte(),r=this.readRawByte(),n=this.readRawByte(),i=this.readRawByte(),s=this.readRawByte(),a=this.readRawByte(),u=this.readRawByte(),f=new o([e,t,r,n,i,s,a,u]);return f},readRawLittleEndian32:function(){var e=this.readRawByte(),t=this.readRawByte(),r=this.readRawByte(),n=this.readRawByte();return 255&e|(255&t)<<8|(255&r)<<16|(255&n)<<24},readBoolean:function(){return 0!=this.readRawByte()},readDoubleAndInt64:function(){var e=this.readRawLittleEndian64(),t=e.readDoubleLE(0),r=new i.Int64LE(e);return{doubleVal:t,int64LE:r}},readInt32:function(){return this.readRawLittleEndian32()},readInt64:function(){var e=this.readRawLittleEndian64(),t=new i.Int64LE(e);return t},readBytes:function(e){if(this.buffer.length-this.curPos<e)throw new Error("Read bytes encountered EOF.");var t=this.curPos;this.curPos+=e;var r=new o(e);return this.buffer.copy(r,0,t,this.curPos),r},readUtfString:function(e){if(this.buffer.length-this.curPos<e)throw new Error("Read UTF string encountered EOF.");var t=this.buffer.toString("utf8",this.curPos,this.curPos+e);return this.curPos+=e,t}}),n.PlainBufferOutputStream=s({constructor:function(e){this.buffer=new o(e),this.buffer.fill(0),this.capacity=e,this.pos=0},getBuffer:function(){return this.buffer},isFull:function(){return this.pos===this.capacity},count:function(){return this.pos},remain:function(){return this.capacity-this.pos},clear:function(){this.pos=0,this.buffer=[]},writeRawByte:function(e){if(this.isFull())throw new Error("The buffer is full");this.buffer[this.pos++]=e},writeRawLittleEndian32:function(e){this.writeRawByte(255&e),this.writeRawByte(e>>8&255),this.writeRawByte(e>>16&255),this.writeRawByte(e>>24&255)},writeRawLittleEndian64:function(e){for(var t=0;t<e.length;t++){var r=e[t]>127?e[t]-256:e[t];this.writeRawByte(255&r)}},writeInt64LE:function(e){var t=e.toBuffer();this.writeRawLittleEndian64(t)},writeDouble:function(e){var t=n.util.Int64.doubleToRawLongBits(e);this.writeRawLittleEndian64(t.toBuffer())},writeBoolean:function(e){e?this.writeRawByte(1):this.writeRawByte(0)},writeBytes:function(e){var t=null;if(e instanceof o?t=e:"string"===typeof e&&(t=new o(e)),this.pos+t.length>this.capacity)throw Error("The buffer is full.");if(e instanceof o)e.copy(this.buffer,this.pos);else{if("string"!==typeof e)throw new Error("expect Buffer or string,but it was:"+typeof e);this.buffer.write(e,this.pos)}this.pos+=t.length}})},{"../core":6,"int64-buffer":33}],20:[function(e,t,r){(function(t){var r=e("../core"),n=e("int64-buffer");r.PlainBufferBuilder={computePrimaryKeyValueSize:function(e){var i=1;if(i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1,e===r.INF_MIN||e===r.INF_MAX||e===r.PK_AUTO_INCR)return i+=1,i;if(e instanceof n.Int64LE)i+=8;else if("string"===typeof e)i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,i+=r.util.string.byteLength(e);else{if(!(e instanceof t))throw new Error("Unsupported primary key type:"+typeof e);i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,i+=e.length}return i},computeVariantValueSize:function(e){return this.computePrimaryKeyValueSize(e)-r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE-1},computePrimaryKeyColumnSize:function(e,t){var n=1;return n+=1+r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,n+=e.length,n+=this.computePrimaryKeyValueSize(t),n+=2,n},computeColumnValueSize:function(e){var i=1;if(i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE+1,"number"===typeof e)i+=r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE;else if("string"===typeof e)i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,i+=r.util.string.byteLength(e);else if(e instanceof t)i+=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,i+=e.length;else if("boolean"===typeof e)i+=1;else{if(!(e instanceof n.Int64LE))throw new Error("Unsupported column type: "+typeof e);i+=r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE}return i},computeVariantValueSize:function(e){return this.computeColumnValueSize(e)-r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE-1},computeColumnSize:function(e,t,n){var i=1;return i+=1+r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,i+=e.length,null!==t&&(i+=this.computeColumnValueSize(t)),void 0!==n&&(i+=1+r.plainBufferConsts.LITTLE_ENDIAN_64_SIZE),i+=2,i},computeUpdateColumnSize:function(e,t,n){var i=this.computeColumnSize(e,t);return n!==r.UpdateType.DELETE&&n!==r.UpdateType.DELETE_ALL||(i+=2),i},computePrimaryKeySize:function(e){for(var t=1,r=0;r<e.length;r++)for(key in e[r])t+=this.computePrimaryKeyColumnSize(key,e[r][key]);return t},computePutRowSize:function(e,t){var n=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE;if(n+=this.computePrimaryKeySize(e),t&&0!=t.length){n+=1;for(var i=0;i<t.length;i++)if(void 0===t[i].timestamp)for(var o in t[i])n+=this.computeColumnSize(o,t[i][o]);else for(var o in t[i]){n+=this.computeColumnSize(o,t[i][o],t[i].timestamp);break}}return n+=2,n},computeUpdateRowSize:function(e,t){var n=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE;if(n+=this.computePrimaryKeySize(e),0!=t.length){n+=1;for(var i=0;i<t.length;i++)for(var o in t[i]){var s=t[i][o];if(!s instanceof Array)throw new Error("Unsupported column type:"+typeof s);for(var a in s)if(o===r.UpdateType.DELETE_ALL)n+=this.computeUpdateColumnSize(s[a],null,o);else{if(o!==r.UpdateType.PUT&&o!==r.UpdateType.DELETE)throw new Error("Expect TableStore.UpdateType but it was:"+o);for(var u in s[a])n+=this.computeUpdateColumnSize(u,s[a][u],o)}break}}return n+=2,n},computeDeleteRowSize:function(e){return size=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE,size+=this.computePrimaryKeySize(e),size+=3,size},serializePrimaryKeyValue:function(e){var t=this.computeVariantValueSize(e),n=new r.PlainBufferOutputStream(t),i=new r.PlainBufferCodedOutputStream(n);return i.writePrimaryKeyValue(e),n.getBuffer()},serializeColumnValue:function(e){var t=this.computeVariantValueSize(e),n=new r.PlainBufferOutputStream(t),i=new r.PlainBufferCodedOutputStream(n);return i.writeColumnValue(e),n.getBuffer()},serializePrimaryKey:function(e){var t=r.plainBufferConsts.LITTLE_ENDIAN_32_SIZE;t+=this.computePrimaryKeySize(e),t+=2;var n=new r.PlainBufferOutputStream(t),i=new r.PlainBufferCodedOutputStream(n),o=0;return i.writeHeader(),o=i.writePrimaryKey(e,o),o=r.plainBufferCrc8.crcInt8(o,0),i.writeRowChecksum(o),n.getBuffer()},serializeForPutRow:function(e,t){var n=this.computePutRowSize(e,t),i=new r.PlainBufferOutputStream(n),o=new r.PlainBufferCodedOutputStream(i),s=0;return o.writeHeader(),s=o.writePrimaryKey(e,s),s=o.writeColumns(t,s),s=r.plainBufferCrc8.crcInt8(s,0),o.writeRowChecksum(s),i.getBuffer()},serializeForUpdateRow:function(e,t){var n=this.computeUpdateRowSize(e,t),i=new r.PlainBufferOutputStream(n),o=new r.PlainBufferCodedOutputStream(i),s=0;return o.writeHeader(),s=o.writePrimaryKey(e,s),s=o.writeUpdateColumns(t,s),s=r.plainBufferCrc8.crcInt8(s,0),o.writeRowChecksum(s),i.getBuffer()},serializeForDeleteRow:function(e){var t=this.computeDeleteRowSize(e),n=new r.PlainBufferOutputStream(t),i=new r.PlainBufferCodedOutputStream(n),o=0;return i.writeHeader(),o=i.writePrimaryKey(e,o),o=i.writeDeleteMarker(o),i.writeRowChecksum(o),n.getBuffer()}}}).call(this,e("buffer").Buffer)},{"../core":6,buffer:40,"int64-buffer":33}],21:[function(e,t,r){t.exports=e("protobufjs").newBuilder({})["import"]({package:"tablestore.filter.proto",messages:[{name:"SingleColumnValueFilter",fields:[{rule:"required",type:"ComparatorType",name:"comparator",id:1},{rule:"required",type:"string",name:"column_name",id:2},{rule:"required",type:"bytes",name:"column_value",id:3},{rule:"required",type:"bool",name:"filter_if_missing",id:4},{rule:"required",type:"bool",name:"latest_version_only",id:5}]},{name:"CompositeColumnValueFilter",fields:[{rule:"required",type:"LogicalOperator",name:"combinator",id:1},{rule:"repeated",type:"Filter",name:"sub_filters",id:2}]},{name:"ColumnPaginationFilter",fields:[{rule:"required",type:"int32",name:"offset",id:1},{rule:"required",type:"int32",name:"limit",id:2}]},{name:"Filter",fields:[{rule:"required",type:"FilterType",name:"type",id:1},{rule:"required",type:"bytes",name:"filter",id:2}]}],enums:[{name:"FilterType",values:[{name:"FT_SINGLE_COLUMN_VALUE",id:1},{name:"FT_COMPOSITE_COLUMN_VALUE",id:2},{name:"FT_COLUMN_PAGINATION",id:3}]},{name:"ComparatorType",values:[{name:"CT_EQUAL",id:1},{name:"CT_NOT_EQUAL",id:2},{name:"CT_GREATER_THAN",id:3},{name:"CT_GREATER_EQUAL",id:4},{name:"CT_LESS_THAN",id:5},{name:"CT_LESS_EQUAL",id:6}]},{name:"LogicalOperator",values:[{name:"LO_NOT",id:1},{name:"LO_AND",id:2},{name:"LO_OR",id:3}]}]}).build()},{protobufjs:36}],22:[function(e,t,r){t.exports=e("protobufjs").newBuilder({})["import"]({package:"tablestore.proto",messages:[{name:"Error",fields:[{rule:"required",type:"string",name:"code",id:1},{rule:"optional",type:"string",name:"message",id:2}]},{name:"PrimaryKeySchema",fields:[{rule:"required",type:"string",name:"name",id:1},{rule:"required",type:"PrimaryKeyType",name:"type",id:2},{rule:"optional",type:"PrimaryKeyOption",name:"option",id:3}]},{name:"PartitionRange",fields:[{rule:"required",type:"bytes",name:"begin",id:1},{rule:"required",type:"bytes",name:"end",id:2}]},{name:"TableOptions",fields:[{rule:"optional",type:"int32",name:"time_to_live",id:1},{rule:"optional",type:"int32",name:"max_versions",id:2},{rule:"optional",type:"BloomFilterType",name:"bloom_filter_type",id:3},{rule:"optional",type:"int32",name:"block_size",id:4},{rule:"optional",type:"int64",name:"deviation_cell_version_in_sec",id:5}]},{name:"TableMeta",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"repeated",type:"PrimaryKeySchema",name:"primary_key",id:2}]},{name:"Condition",fields:[{rule:"required",type:"RowExistenceExpectation",name:"row_existence",id:1},{rule:"optional",type:"bytes",name:"column_condition",id:2}]},{name:"CapacityUnit",fields:[{rule:"optional",type:"int32",name:"read",id:1},{rule:"optional",type:"int32",name:"write",id:2}]},{name:"ReservedThroughputDetails",fields:[{rule:"required",type:"CapacityUnit",name:"capacity_unit",id:1},{rule:"required",type:"int64",name:"last_increase_time",id:2},{rule:"optional",type:"int64",name:"last_decrease_time",id:3}]},{name:"ReservedThroughput",fields:[{rule:"required",type:"CapacityUnit",name:"capacity_unit",id:1}]},{name:"ConsumedCapacity",fields:[{rule:"required",type:"CapacityUnit",name:"capacity_unit",id:1}]},{name:"CreateTableRequest",fields:[{rule:"required",type:"TableMeta",name:"table_meta",id:1},{rule:"required",type:"ReservedThroughput",name:"reserved_throughput",id:2},{rule:"optional",type:"TableOptions",name:"table_options",id:3},{rule:"repeated",type:"PartitionRange",name:"partitions",id:4}]},{name:"CreateTableResponse",fields:[]},{name:"UpdateTableRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"optional",type:"ReservedThroughput",name:"reserved_throughput",id:2},{rule:"optional",type:"TableOptions",name:"table_options",id:3}]},{name:"UpdateTableResponse",fields:[{rule:"required",type:"ReservedThroughputDetails",name:"reserved_throughput_details",id:1},{rule:"required",type:"TableOptions",name:"table_options",id:2}]},{name:"DescribeTableRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1}]},{name:"DescribeTableResponse",fields:[{rule:"required",type:"TableMeta",name:"table_meta",id:1},{rule:"required",type:"ReservedThroughputDetails",name:"reserved_throughput_details",id:2},{rule:"required",type:"TableOptions",name:"table_options",id:3},{rule:"required",type:"TableStatus",name:"table_status",id:4},{rule:"repeated",type:"bytes",name:"shard_splits",id:6}]},{name:"ListTableRequest",fields:[]},{name:"ListTableResponse",fields:[{rule:"repeated",type:"string",name:"table_names",id:1}]},{name:"DeleteTableRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1}]},{name:"DeleteTableResponse",fields:[]},{name:"LoadTableRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1}]},{name:"LoadTableResponse",fields:[]},{name:"UnloadTableRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1}]},{name:"UnloadTableResponse",fields:[]},{name:"TimeRange",fields:[{rule:"optional",type:"int64",name:"start_time",id:1},{rule:"optional",type:"int64",name:"end_time",id:2},{rule:"optional",type:"int64",name:"specific_time",id:3}]},{name:"ReturnContent",fields:[{rule:"optional",type:"ReturnType",name:"return_type",id:1}]},{name:"GetRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"required",type:"bytes",name:"primary_key",id:2},{rule:"repeated",type:"string",name:"columns_to_get",id:3},{rule:"optional",type:"TimeRange",name:"time_range",id:4},{rule:"optional",type:"int32",name:"max_versions",id:5},{rule:"optional",type:"bool",name:"cache_blocks",id:6,options:{default:!0}},{rule:"optional",type:"bytes",name:"filter",id:7},{rule:"optional",type:"string",name:"start_column",id:8},{rule:"optional",type:"string",name:"end_column",id:9},{rule:"optional",type:"bytes",name:"token",id:10}]},{name:"GetRowResponse",fields:[{rule:"required",type:"ConsumedCapacity",name:"consumed",id:1},{rule:"required",type:"bytes",name:"row",id:2},{rule:"optional",type:"bytes",name:"next_token",id:3}]},{name:"UpdateRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"required",type:"bytes",name:"row_change",id:2},{rule:"required",type:"Condition",name:"condition",id:3},{rule:"optional",type:"ReturnContent",name:"return_content",id:4}]},{name:"UpdateRowResponse",fields:[{rule:"required",type:"ConsumedCapacity",name:"consumed",id:1},{rule:"optional",type:"bytes",name:"row",id:2}]},{name:"PutRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"required",type:"bytes",name:"row",id:2},{rule:"required",type:"Condition",name:"condition",id:3},{rule:"optional",type:"ReturnContent",name:"return_content",id:4}]},{name:"PutRowResponse",fields:[{rule:"required",type:"ConsumedCapacity",name:"consumed",id:1},{rule:"optional",type:"bytes",name:"row",id:2}]},{name:"DeleteRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"required",type:"bytes",name:"primary_key",id:2},{rule:"required",type:"Condition",name:"condition",id:3},{rule:"optional",type:"ReturnContent",name:"return_content",id:4}]},{name:"DeleteRowResponse",fields:[{rule:"required",type:"ConsumedCapacity",name:"consumed",id:1},{rule:"optional",type:"bytes",name:"row",id:2}]},{name:"TableInBatchGetRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"repeated",type:"bytes",name:"primary_key",id:2},{rule:"repeated",type:"bytes",name:"token",id:3},{rule:"repeated",type:"string",name:"columns_to_get",id:4},{rule:"optional",type:"TimeRange",name:"time_range",id:5},{rule:"optional",type:"int32",name:"max_versions",id:6},{rule:"optional",type:"bool",name:"cache_blocks",id:7,options:{default:!0}},{rule:"optional",type:"bytes",name:"filter",id:8},{rule:"optional",type:"string",name:"start_column",id:9},{rule:"optional",type:"string",name:"end_column",id:10}]},{name:"BatchGetRowRequest",fields:[{rule:"repeated",type:"TableInBatchGetRowRequest",name:"tables",id:1}]},{name:"RowInBatchGetRowResponse",fields:[{rule:"required",type:"bool",name:"is_ok",id:1},{rule:"optional",type:"Error",name:"error",id:2},{rule:"optional",type:"ConsumedCapacity",name:"consumed",id:3},{rule:"optional",type:"bytes",name:"row",id:4},{rule:"optional",type:"bytes",name:"next_token",id:5}]},{name:"TableInBatchGetRowResponse",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"repeated",type:"RowInBatchGetRowResponse",name:"rows",id:2}]},{name:"BatchGetRowResponse",fields:[{rule:"repeated",type:"TableInBatchGetRowResponse",name:"tables",id:1}]},{name:"RowInBatchWriteRowRequest",fields:[{rule:"required",type:"OperationType",name:"type",id:1},{rule:"required",type:"bytes",name:"row_change",id:2},{rule:"required",type:"Condition",name:"condition",id:3},{rule:"optional",type:"ReturnContent",name:"return_content",id:4}]},{name:"TableInBatchWriteRowRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"repeated",type:"RowInBatchWriteRowRequest",name:"rows",id:2}]},{name:"BatchWriteRowRequest",fields:[{rule:"repeated",type:"TableInBatchWriteRowRequest",name:"tables",id:1}]},{name:"RowInBatchWriteRowResponse",fields:[{rule:"required",type:"bool",name:"is_ok",id:1},{rule:"optional",type:"Error",name:"error",id:2},{rule:"optional",type:"ConsumedCapacity",name:"consumed",id:3},{rule:"optional",type:"bytes",name:"row",id:4}]},{name:"TableInBatchWriteRowResponse",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"repeated",type:"RowInBatchWriteRowResponse",name:"rows",id:2}]},{name:"BatchWriteRowResponse",fields:[{rule:"repeated",type:"TableInBatchWriteRowResponse",name:"tables",id:1}]},{name:"GetRangeRequest",fields:[{rule:"required",type:"string",name:"table_name",id:1},{rule:"required",type:"Direction",name:"direction",id:2},{rule:"repeated",type:"string",name:"columns_to_get",id:3},{rule:"optional",type:"TimeRange",name:"time_range",id:4},{rule:"optional",type:"int32",name:"max_versions",id:5},{rule:"optional",type:"int32",name:"limit",id:6},{rule:"required",type:"bytes",name:"inclusive_start_primary_key",id:7},{rule:"required",type:"bytes",name:"exclusive_end_primary_key",id:8},{rule:"optional",type:"bool",name:"cache_blocks",id:9,options:{default:!0}},{rule:"optional",type:"bytes",name:"filter",id:10},{rule:"optional",type:"string",name:"start_column",id:11},{rule:"optional",type:"string",name:"end_column",id:12},{rule:"optional",type:"bytes",name:"token",id:13}]},{name:"GetRangeResponse",fields:[{rule:"required",type:"ConsumedCapacity",name:"consumed",id:1},{rule:"required",type:"bytes",name:"rows",id:2},{rule:"optional",type:"bytes",name:"next_start_primary_key",id:3},{rule:"optional",type:"bytes",name:"next_token",id:4}]}],enums:[{name:"PrimaryKeyType",values:[{name:"INTEGER",id:1},{name:"STRING",id:2},{name:"BINARY",id:3}]},{name:"PrimaryKeyOption",values:[{name:"AUTO_INCREMENT",id:1}]},{name:"BloomFilterType",values:[{name:"NONE",id:1},{name:"CELL",id:2},{name:"ROW",id:3}]},{name:"TableStatus",values:[{name:"ACTIVE",id:1},{name:"INACTIVE",id:2},{name:"LOADING",id:3},{name:"UNLOADING",id:4},{name:"UPDATING",id:5}]},{name:"RowExistenceExpectation",values:[{name:"IGNORE",id:0},{name:"EXPECT_EXIST",id:1},{name:"EXPECT_NOT_EXIST",id:2}]},{name:"ReturnType",values:[{name:"RT_NONE",id:0},{name:"RT_PK",id:1}]},{name:"OperationType",values:[{name:"PUT",id:1},{name:"UPDATE",id:2},{name:"DELETE",id:3}]},{name:"Direction",values:[{name:"FORWARD",id:0},{name:"BACKWARD",id:1}]}]}).build()},{protobufjs:36}],23:[function(e,t,r){var n=e("./core"),i=n.util.inherit,o=n.util.nodeRequire("domain"),s={success:1,error:1,complete:1};function a(e){return Object.prototype.hasOwnProperty.call(s,e._asm.currentState)}function u(e,t){this.currentState=t||null,this.states=e||{}}u.prototype.runTo=function(e,t,r,n){"function"===typeof e&&(n=r,r=t,t=e,e=null);var i=this,o=i.states[i.currentState];o.fn.call(r||i,n,(function(n){if(n){if(r.logger&&r.logger.log(i.currentState,"->",o.fail,n),!o.fail)return t?t(n):null;i.currentState=o.fail}else{if(r.logger&&r.logger.log(i.currentState,"->",o.accept),!o.accept)return t?t():null;i.currentState=o.accept}if(i.currentState===e)return t?t(n):null;i.runTo(e,t,r,n)}))},u.prototype.addState=function(e,t,r,n){return"function"===typeof t?(n=t,t=null,r=null):"function"===typeof r&&(n=r,r=null),this.currentState||(this.currentState=e),this.states[e]={accept:t,fail:r,fn:n},this};var f=new u;f.setupStates=function(){var e=function(e,t){var r=this;r.response.error;r.emit(r._asm.currentState,(function(e){if(e)if(a(r)){if(!(o&&r.domain instanceof o.Domain))throw e;e.domainEmitter=r,e.domain=r.domain,e.domainThrown=!1,r.domain.emit("error",e)}else r.response.error=e,t(e);else t(r.response.error)}))};this.addState("restart","build","error",(function(e,t){return e=this.response.error,e?(e.retryable=n.DefaultRetryPolicy.shouldRetry(this.response.retryCount,this.response.error,this.response.request.operation),e.retryable?void(this.response.retryCount<n.DefaultRetryPolicy.maxRetryTimes?(this.response.retryCount++,t()):t(e)):t(e)):t()})),this.addState("build","afterBuild","restart",e),this.addState("afterBuild","sign","restart",e),this.addState("sign","send","retry",e),this.addState("retry","afterRetry","afterRetry",e),this.addState("afterRetry","sign","error",e),this.addState("send","validateResponse","retry",e),this.addState("validateResponse","extractData","extractError",e),this.addState("extractError","extractData","retry",e),this.addState("extractData","success","retry",e),this.addState("success","complete","complete",e),this.addState("error","complete","complete",e),this.addState("complete",null,null,e)},f.setupStates(),n.Request=i({constructor:function(e,t,r){var i=new n.Endpoint(e.endpoint),s=e.region;this.config=e,void 0!==e.maxRetries&&(n.DefaultRetryPolicy.maxRetryTimes=e.maxRetries),this.domain=o&&o.active,this.operation=t,this.params=r||{},this.httpRequest=new n.HttpRequest(i,s),this.startTime=n.util.date.getDate(),this.response=new n.Response(this),this.restartCount=0,this._asm=new u(f.states,"build"),n.SequentialExecutor.call(this),this.emit=this.emitEvent},send:function(e){return e&&this.on("complete",(function(t){e.call(t,t.error,t.data)})),this.runTo(),this.response},build:function(e){return this.runTo("send",e)},runTo:function(e,t){return this._asm.runTo(e,t,this),this},emitEvent:function(e,t,r){"function"===typeof t&&(r=t,t=null),r||(r=function(){}),t||(t=this.eventParameters(e,this.response));var i=n.SequentialExecutor.prototype.emit;i.call(this,e,t,(function(e){e&&(this.response.error=e),r.call(this,e)}))},eventParameters:function(e){switch(e){case"validate":case"sign":case"build":case"afterBuild":return[this];case"error":return[this.response.error,this.response];default:return[this.response]}}}),n.util.mixin(n.Request,n.SequentialExecutor),n.Response=i({constructor:function(e){this.request=e,this.data=null,this.error=null,this.retryCount=0,this.httpResponse=new n.HttpResponse}})},{"./core":6}],24:[function(e,t,r){var n=e("../core");n.DefaultRetryPolicy={maxRetryTimes:20,maxRetryDelay:3,scaleFactor:2,serverThrottlingExceptionDelayFactor:.5,stabilityExceptionDelayFactor:.2,_maxRetryTimeReached:function(e,t,r){return e>=n.DefaultRetryPolicy.maxRetryTimes},isRepeatableApi:function(e){return n.RetryUtil.isRepeatableApi(e)},_canRetry:function(e,t,r){return!!n.RetryUtil.shouldRetryNoMatterWhichApi(t)||!(!n.DefaultRetryPolicy.isRepeatableApi(r)||!n.RetryUtil.shouldRetryWhenApiRepeatable(e,t,r))},getRetryDelay:function(e,t){var r;r=n.RetryUtil.isServerThrottlingException(t)?n.DefaultRetryPolicy.serverThrottlingExceptionDelayFactor:n.DefaultRetryPolicy.stabilityExceptionDelayFactor;var i=r*Math.pow(n.DefaultRetryPolicy.scaleFactor,e);i>=n.DefaultRetryPolicy.maxRetryDelay&&(i=n.DefaultRetryPolicy.maxRetryDelay);var o=.5*i+.5*i*Math.random();return o},shouldRetry:function(e,t,r){return!n.DefaultRetryPolicy._maxRetryTimeReached(e,t,r)&&!!n.DefaultRetryPolicy._canRetry(e,t,r)}}},{"../core":6}],25:[function(e,t,r){var n=e("../core");n.RetryUtil={shouldRetryNoMatterWhichApi:function(e){var t=e.code,r=e.message;return"OTSRowOperationConflict"==t||"OTSNotEnoughCapacityUnit"==t||"OTSTableNotReady"==t||"OTSPartitionUnavailable"==t||"OTSServerBusy"==t||"OTSOperationThrottled"==t||"OTSQuotaExhausted"==t&&"Too frequent table operations."==r},isRepeatableApi:function(e){e=n.util.string.upperFirst(e);var t=["ListTable","DescribeTable","GetRow","BatchGetRow","GetRange"];for(var r in t)if(t[r]===e)return!0},shouldRetryWhenApiRepeatable:function(e,t,r){var n=t.code;t.message;return"OTSTimeout"==n||"OTSInternalServerError"==n||"OTSServerUnavailable"==n||"NetworkingError"==n||(500==n||502==n||503==n)},isServerThrottlingException:function(e){var t=e.code,r=e.message;return"OTSServerBusy"==t||"OTSNotEnoughCapacityUnit"==t||"OTSOperationThrottled"==t||"OTSQuotaExhausted"==t&&"Too frequent table operations."==r}}},{"../core":6}],26:[function(e,t,r){(function(t){var r=e("./core"),n=r.util.nodeRequire("domain");r.SequentialExecutor=r.util.inherit({constructor:function(){this.domain=n&&n.active,this._events={}},listeners:function(e){return this._events[e]?this._events[e].slice(0):[]},on:function(e,t){return this._events[e]?this._events[e].push(t):this._events[e]=[t],this},onAsync:function(e,t){return t._isAsync=!0,this.on(e,t)},removeListener:function(e,t){var r=this._events[e];if(r){for(var n=r.length,i=-1,o=0;o<n;++o)r[o]===t&&(i=o);i>-1&&r.splice(i,1)}return this},removeAllListeners:function(e){return e?delete this._events[e]:this._events={},this},emit:function(e,r,i){i||(i=this.unhandledErrorCallback),n&&this.domain instanceof n.Domain&&this.domain.enter(),"aliyun"==t.env.DEBUG&&console.log("emit",e);var o=this.listeners(e),s=o.length;return this.callListeners(o,r,i),s>0},callListeners:function(e,t,r){if(0===e.length)r.call(this),n&&this.domain instanceof n.Domain&&this.domain.exit();else{var i=e.shift();if(i._isAsync){var o=function(i){i?(r.call(this,i),n&&this.domain instanceof n.Domain&&this.domain.exit()):this.callListeners(e,t,r)}.bind(this);i.apply(this,t.concat([o]))}else try{i.apply(this,t),this.callListeners(e,t,r)}catch(s){r.call(this,s),n&&this.domain instanceof n.Domain&&this.domain.exit()}}},addListeners:function(e){var t=this;return e._events&&(e=e._events),r.util.each(e,(function(e,n){"function"===typeof n&&(n=[n]),r.util.arrayEach(n,(function(r){t.on(e,r)}))})),t},addNamedListener:function(e,t,r){return this[e]=r,this.addListener(t,r),this},addNamedAsyncListener:function(e,t,r){return r._isAsync=!0,this.addNamedListener(e,t,r)},addNamedListeners:function(e){var t=this;return e((function(){t.addNamedListener.apply(t,arguments)}),(function(){t.addNamedAsyncListener.apply(t,arguments)})),this}}),r.SequentialExecutor.prototype.addListener=r.SequentialExecutor.prototype.on,r.SequentialExecutor.prototype.addAsyncListener=r.SequentialExecutor.prototype.onAsync}).call(this,e("_process"))},{"./core":6,_process:50}],27:[function(e,t,r){(function(r){var n=e("./core"),i=n.util.inherit;n.Signer=i({constructor:function(e){this.request=e},addAuthorization:function(e,t){this.request.headers["x-ots-date"]=n.util.date.iso8601(t),this.request.headers["x-ots-accesskeyid"]=e.accessKeyId,e.securityToken&&(this.request.headers["x-ots-ststoken"]=e.securityToken);var r=this.sign(e.secretAccessKey,this.stringToSign());this.request.headers["x-ots-signature"]=r},stringToSign:function(){var e=this.request,t=[];t.push(e.path),t.push(e.method+"\n");var r=this.canonicalizedHeaders();return r&&t.push(r),t.join("\n")+"\n"},canonicalizedHeaders:function(){var e=[];n.util.each(this.request.headers,(function(t){t.match(/^x-ots-/i)&&e.push(t)})),e.sort((function(e,t){return e.toLowerCase()<t.toLowerCase()?-1:1}));var t=[];return n.util.arrayEach.call(this,e,(function(e){t.push(e.toLowerCase()+":"+String(this.request.headers[e]))})),t.join("\n")},sign:function(e,t){return"aliyun"==r.env.DEBUG&&(console.log("----------- sign string start -----------"),console.log(t),console.log("----------- sign string end -----------")),n.util.crypto.hmac(e,t,"base64","sha1")}}),t.exports=n.Signer}).call(this,e("_process"))},{"./core":6,_process:50}],28:[function(e,t,r){(function(r){var n=e("./core"),i=e("../bower_components/jsSHA/src/sha.js"),o=e("../bower_components/spark-md5/spark-md5.js"),s=e("int64-buffer"),a=e("buffer/").Buffer;n.util={engine:function(){return n.util.isBrowser()&&"undefined"!==typeof navigator?navigator.userAgent:r.platform+"/"+r.version},userAgent:function(){return""},isBrowser:function(){return"undefined"!==typeof window},isNode:function(){return!n.util.isBrowser()},nodeRequire:function(t){if(n.util.isNode())return e(t)},hexToBase64:function(e){return btoa(String.fromCharCode.apply(null,e.replace(/\r|\n/g,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")))},topEscape:function(e){var t="",r=0;e=e.toString();var n=/(^[a-zA-Z0-9-_.~]*)/;while(r<e.length){var i=n.exec(e.substr(r));if(null!=i&&i.length>1&&""!=i[1])t+=i[1],r+=i[1].length;else{if(" "==e[r])t+="%20";else{var o=e.charCodeAt(r),s=o.toString(16);t+="%"+(s.length<2?"0":"")+s.toUpperCase()}r++}}return t},popEscape:function(e){return e=e.toString(),e=encodeURIComponent(e).replace(/\!/gi,"%21").replace(/\'/gi,"%27").replace(/\(/gi,"%28").replace(/\)/gi,"%29").replace(/\*/gi,"%2A"),e},opensearchEscape:function(e){return encodeURIComponent(e).replace(/\!/gi,"%21").replace(/\'/gi,"%27").replace(/\(/gi,"%28").replace(/\)/gi,"%29").replace(/\*/gi,"%2A")},uriEscape:function(e){var t=encodeURIComponent(e);return t=t.replace(/[^A-Za-z0-9_.~\-%]+/g,escape),t=t.replace(/[*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})),t},uriEscapePath:function(e){var t=[];return n.util.arrayEach(e.split("/"),(function(e){t.push(n.util.uriEscape(e))})),t.join("/")},urlParse:function(t){return e("url").parse(t)},urlFormat:function(t){return e("url").format(t)},uuid:function(){var e=(new Date).getTime();window.performance&&"function"===typeof window.performance.now&&(e+=performance.now());var t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var r=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?r:3&r|8).toString(16)}));return t},queryParamsToString:function(e){var t=[],r=n.util.uriEscape,i=Object.keys(e).sort();return n.util.arrayEach(i,(function(i){var o=e[i],s=r(i),a=s;if(Array.isArray(o)){var u=[];n.util.arrayEach(o,(function(e){u.push(r(e))})),a=s+"="+u.sort().join("&"+s+"=")}else void 0!==o&&null!==o&&(a=s+"="+r(o));t.push(a)})),t.join("&")},readFileSync:function(t){return"undefined"!==typeof window?null:e("fs").readFileSync(t,"utf-8")},base64:{encode:function(e){if("number"===typeof e)throw util.error(new Error("Cannot base64 encode number "+e));if(null===e||"undefined"===typeof e)return e;var t="function"===typeof n.util.Buffer.from&&n.util.Buffer.from!==Uint8Array.from?n.util.Buffer.from(e):new n.util.Buffer(e);return t.toString("base64")},decode:function(e){if("number"===typeof e)throw util.error(new Error("Cannot base64 decode number "+e));return null===e||"undefined"===typeof e?e:"function"===typeof n.util.Buffer.from&&n.util.Buffer.from!==Uint8Array.from?n.util.Buffer.from(e,"base64"):new n.util.Buffer(e,"base64")}},Buffer:a,buffer:{concat:function(e){var t,r=0,i=0,o=null;for(t=0;t<e.length;t++)r+=e[t].length;for(o=new n.util.Buffer(r),t=0;t<e.length;t++)e[t].copy(o,i),i+=e[t].length;return o}},Int64:{doubleToRawLongBits:function(e){var t=new a(8);t.fill(0),t.writeDoubleLE(e,0);var r=new s.Int64LE(t);return r}},string:{byteLength:function(t){if(null===t||void 0===t)return 0;if("string"===typeof t&&(t=new a(t)),"number"===typeof t.byteLength)return t.byteLength;if("number"===typeof t.length)return t.length;if("number"===typeof t.size)return t.size;if("string"===typeof t.path)return e("fs").lstatSync(t.path).size;throw n.util.error(new Error("Cannot determine length of "+t),{object:t})},upperFirst:function(e){return e[0].toUpperCase()+e.substr(1)},lowerFirst:function(e){return e[0].toLowerCase()+e.substr(1)}},jamespath:{query:function(e,t){if(!t)return[];var r=[],i=e.split(/\s+or\s+/);return n.util.arrayEach.call(this,i,(function(e){var i=[t],o=e.split(".");if(n.util.arrayEach.call(this,o,(function(e){var t=e.match("^(.+?)(?:\\[(-?\\d+|\\*)\\])?$"),r=[];if(n.util.arrayEach.call(this,i,(function(e){"*"===t[1]?n.util.arrayEach.call(this,e,(function(e){r.push(e)})):e.hasOwnProperty(t[1])&&r.push(e[t[1]])})),i=r,t[2]&&(r=[],n.util.arrayEach.call(this,i,(function(e){if(Array.isArray(e))if("*"===t[2])r=r.concat(e);else{var n=parseInt(t[2],10);n<0&&(n=e.length+n),r.push(e[n])}})),i=r),0===i.length)return n.util.abort})),i.length>0)return r=i,n.util.abort})),r},find:function(e,t){return n.util.jamespath.query(e,t)[0]}},date:{getDate:function(){return new Date},top:function(e,t){function r(e){return e.toString().length<2?"0"+e:e}return t=t||"%Y-%M-%dT%H:%m:%sZ",t.replace(/%([a-zA-Z])/g,(function(t,n){switch(n){case"Y":return e.getUTCFullYear();case"M":return r(e.getUTCMonth()+1);case"d":return r(e.getUTCDate());case"H":return r(e.getUTCHours());case"m":return r(e.getUTCMinutes());case"s":return r(e.getUTCSeconds());default:throw new Error("Unsupported format code: "+n)}}))},iso8601:function(e){return void 0===e&&(e=n.util.date.getDate()),e.toISOString()},rfc822:function(e){return void 0===e&&(e=n.util.date.getDate()),e.toUTCString()},unixSeconds:function(e){return void 0===e&&(e=n.util.date.getDate()),Math.floor(e.getTime()/1e3)},unixMilliseconds:function(e){return void 0===e&&(e=n.util.date.getDate()),e.getTime()},from:function(e){return"number"===typeof e?10==e.toString().length?new Date(1e3*e):new Date(e):"[object Date]"===Object.prototype.toString.call(e)?e:new Date(e)},format:function(e,t){return t||(t="unixSeconds"),n.util.date[t](n.util.date.from(e))}},crypto:{crc32Table:[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],crc32:function(e){var t=n.util.crypto.crc32Table,r=~0;"string"===typeof e&&(e=new a(e));for(var i=0;i<e.length;i++){var o=e.readUInt8(i);r=r>>>8^t[255&(r^o)]}return~r>>>0},hmac:function(e,t,r,n){if(r||(r="binary"),"buffer"===r)return r=void 0,"";if(n||(n="sha256"),"string"!=typeof t)return"";var o;switch(n){case"md5":return"";case"sha1":o=new i("SHA-1","TEXT");break;case"sha256":o=new i("SHA-256","TEXT");break;case"sha512":o=new i("SHA-512","TEXT");break;default:return""}switch(o.setHMACKey(e,"TEXT"),o.update(t),r){case"binary":return o.getHMAC("BYTES");case"hex":return o.getHMAC("HEX");case"base64":return o.getHMAC("B64");default:return""}},md5:function(e,t){if(t||(t="binary"),"buffer"===t)return t=void 0,"";if("string"===typeof e)switch(t){case"binary":return o.hash(e,!0);case"hex":return o.hash(e);case"base64":return n.util.hexToBase64(o.hash(e));default:return""}else switch(t){case"binary":return o.ArrayBuffer.hash(e,!0);case"hex":return o.ArrayBuffer.hash(e);case"base64":return n.util.hexToBase64(o.ArrayBuffer.hash(e));default:return""}},sha256:function(e,t){return""},toHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push(("0"+e.charCodeAt(r).toString(16)).substr(-2,2));return t.join("")}},abort:{},each:function(e,t){for(var r in e)if(e.hasOwnProperty(r)){var i=t.call(this,r,e[r]);if(i===n.util.abort)break}},arrayEach:function(e,t){for(var r in e)if(e.hasOwnProperty(r)){var i=t.call(this,e[r],parseInt(r,10));if(i===n.util.abort)break}},update:function(e,t){return n.util.each(t,(function(t,r){e[t]=r})),e},merge:function(e,t){return n.util.update(n.util.copy(e),t)},copy:function(e){if(null===e||void 0===e)return e;var t={};for(var r in e)t[r]=e[r];return t},isEmpty:function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},isType:function(e,t){return"function"===typeof t&&(t=n.util.typeName(t)),Object.prototype.toString.call(e)==="[object "+t+"]"},typeName:function(e){if(e.hasOwnProperty("name"))return e.name;var t=e.toString(),r=t.match(/^\s*function (.+)\(/);return r?r[1]:t},error:function(e,t){var r=null;return"string"===typeof e.message&&""!==e.message&&("string"===typeof t||t&&t.message)&&(r=n.util.copy(e),r.message=e.message),e.message=e.message||null,"string"===typeof t?e.message=t:n.util.update(e,t),"function"===typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=e.name||e.code||"Error",e.time=new Date,r&&(e.originalError=r),e},inherit:function(e,t){var r=null;if(void 0===t)t=e,e=Object,r={};else{var i=function(){};i.prototype=e.prototype,r=new i}return t.constructor===Object&&(t.constructor=function(){if(e!==Object)return e.apply(this,arguments)}),t.constructor.prototype=r,n.util.update(t.constructor.prototype,t),t.constructor.__super__=e,t.constructor},mixin:function(){for(var e=arguments[0],t=1;t<arguments.length;t++)for(var r in arguments[t].prototype){var n=arguments[t].prototype[r];"constructor"!=r&&(e.prototype[r]=n)}return e},hideProperties:function(e,t){"function"===typeof Object.defineProperty&&n.util.arrayEach(t,(function(t){Object.defineProperty(e,t,{enumerable:!1,writable:!0,configurable:!0})}))}},t.exports=n.util}).call(this,e("_process"))},{"../bower_components/jsSHA/src/sha.js":1,"../bower_components/spark-md5/spark-md5.js":2,"./core":6,_process:50,"buffer/":30,fs:37,"int64-buffer":33,url:76}],29:[function(e,t,r){"use strict";r.byteLength=l,r.toByteArray=h,r.fromByteArray=d;for(var n=[],i=[],o="undefined"!==typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function f(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function l(e){return 3*e.length/4-f(e)}function h(e){var t,r,n,s,a,u=e.length;s=f(e),a=new o(3*u/4-s),r=s>0?u-4:u;var l=0;for(t=0;t<r;t+=4)n=i[e.charCodeAt(t)]<<18|i[e.charCodeAt(t+1)]<<12|i[e.charCodeAt(t+2)]<<6|i[e.charCodeAt(t+3)],a[l++]=n>>16&255,a[l++]=n>>8&255,a[l++]=255&n;return 2===s?(n=i[e.charCodeAt(t)]<<2|i[e.charCodeAt(t+1)]>>4,a[l++]=255&n):1===s&&(n=i[e.charCodeAt(t)]<<10|i[e.charCodeAt(t+1)]<<4|i[e.charCodeAt(t+2)]>>2,a[l++]=n>>8&255,a[l++]=255&n),a}function c(e){return n[e>>18&63]+n[e>>12&63]+n[e>>6&63]+n[63&e]}function p(e,t,r){for(var n,i=[],o=t;o<r;o+=3)n=(e[o]<<16)+(e[o+1]<<8)+e[o+2],i.push(c(n));return i.join("")}function d(e){for(var t,r=e.length,i=r%3,o="",s=[],a=16383,u=0,f=r-i;u<f;u+=a)s.push(p(e,u,u+a>f?f:u+a));return 1===i?(t=e[r-1],o+=n[t>>2],o+=n[t<<4&63],o+="=="):2===i&&(t=(e[r-2]<<8)+e[r-1],o+=n[t>>10],o+=n[t>>4&63],o+=n[t<<2&63],o+="="),s.push(o),s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],30:[function(e,t,r){(function(t){"use strict";var n=e("base64-js"),i=e("ieee754"),o=e("isarray");function s(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"===typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(t){return!1}}function a(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function u(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=f.prototype):(null===e&&(e=new f(t)),e.length=t),e}function f(e,t,r){if(!f.TYPED_ARRAY_SUPPORT&&!(this instanceof f))return new f(e,t,r);if("number"===typeof e){if("string"===typeof t)throw new Error("If encoding is specified then the first argument must be a string");return p(this,e)}return l(this,e,t,r)}function l(e,t,r,n){if("number"===typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&t instanceof ArrayBuffer?g(e,t,r,n):"string"===typeof t?d(e,t,r):m(e,t)}function h(e){if("number"!==typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function c(e,t,r,n){return h(t),t<=0?u(e,t):void 0!==r?"string"===typeof n?u(e,t).fill(r,n):u(e,t).fill(r):u(e,t)}function p(e,t){if(h(t),e=u(e,t<0?0:0|w(t)),!f.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t,r){if("string"===typeof r&&""!==r||(r="utf8"),!f.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|v(t,r);e=u(e,n);var i=e.write(t,r);return i!==n&&(e=e.slice(0,i)),e}function y(e,t){var r=t.length<0?0:0|w(t.length);e=u(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function g(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),f.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=f.prototype):e=y(e,t),e}function m(e,t){if(f.isBuffer(t)){var r=0|w(t.length);return e=u(e,r),0===e.length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!==typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!==typeof t.length||te(t.length)?u(e,0):y(e,t);if("Buffer"===t.type&&o(t.data))return y(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function w(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function b(e){return+e!=e&&(e=0),f.alloc(+e)}function v(e,t){if(f.isBuffer(e))return e.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!==typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return X(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return J(e).length;default:if(n)return X(e).length;t=(""+t).toLowerCase(),n=!0}}function E(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if(r>>>=0,t>>>=0,r<=t)return"";e||(e="utf8");while(1)switch(e){case"hex":return M(this,t,r);case"utf8":case"utf-8":return N(this,t,r);case"ascii":return U(this,t,r);case"latin1":case"binary":return k(this,t,r);case"base64":return x(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return D(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function T(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function _(e,t,r,n,i){if(0===e.length)return-1;if("string"===typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"===typeof t&&(t=f.from(t,n)),f.isBuffer(t))return 0===t.length?-1:S(e,t,r,n,i);if("number"===typeof t)return t&=255,f.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):S(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function S(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&(n=String(n).toLowerCase(),"ucs2"===n||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function f(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var l=-1;for(o=r;o<a;o++)if(f(e,o)===f(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var h=!0,c=0;c<u;c++)if(f(e,o+c)!==f(t,c)){h=!1;break}if(h)return o}return-1}function R(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n),n>i&&(n=i)):n=i;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function C(e,t,r,n){return ee(X(t,e.length-r),e,r,n)}function L(e,t,r,n){return ee($(t),e,r,n)}function I(e,t,r,n){return L(e,t,r,n)}function A(e,t,r,n){return ee(J(t),e,r,n)}function B(e,t,r,n){return ee(Q(t,e.length-r),e,r,n)}function x(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function N(e,t,r){r=Math.min(e.length,r);var n=[],i=t;while(i<r){var o,s,a,u,f=e[i],l=null,h=f>239?4:f>223?3:f>191?2:1;if(i+h<=r)switch(h){case 1:f<128&&(l=f);break;case 2:o=e[i+1],128===(192&o)&&(u=(31&f)<<6|63&o,u>127&&(l=u));break;case 3:o=e[i+1],s=e[i+2],128===(192&o)&&128===(192&s)&&(u=(15&f)<<12|(63&o)<<6|63&s,u>2047&&(u<55296||u>57343)&&(l=u));break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128===(192&o)&&128===(192&s)&&128===(192&a)&&(u=(15&f)<<18|(63&o)<<12|(63&s)<<6|63&a,u>65535&&u<1114112&&(l=u))}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}return O(n)}r.Buffer=f,r.SlowBuffer=b,r.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:s(),r.kMaxLength=a(),f.poolSize=8192,f._augment=function(e){return e.__proto__=f.prototype,e},f.from=function(e,t,r){return l(null,e,t,r)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(e,t,r){return c(null,e,t,r)},f.allocUnsafe=function(e){return p(null,e)},f.allocUnsafeSlow=function(e){return p(null,e)},f.isBuffer=function(e){return!(null==e||!e._isBuffer)},f.compare=function(e,t){if(!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},f.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return f.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=f.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(!f.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},f.byteLength=v,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)T(this,t,t+1);return this},f.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)T(this,t,t+3),T(this,t+1,t+2);return this},f.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)T(this,t,t+7),T(this,t+1,t+6),T(this,t+2,t+5),T(this,t+3,t+4);return this},f.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?N(this,0,e):E.apply(this,arguments)},f.prototype.equals=function(e){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===f.compare(this,e)},f.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},f.prototype.compare=function(e,t,r,n,i){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,s=r-t,a=Math.min(o,s),u=this.slice(n,i),l=e.slice(t,r),h=0;h<a;++h)if(u[h]!==l[h]){o=u[h],s=l[h];break}return o<s?-1:s<o?1:0},f.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},f.prototype.indexOf=function(e,t,r){return _(this,e,t,r,!0)},f.prototype.lastIndexOf=function(e,t,r){return _(this,e,t,r,!1)},f.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"===typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return R(this,e,t,r);case"utf8":case"utf-8":return C(this,e,t,r);case"ascii":return L(this,e,t,r);case"latin1":case"binary":return I(this,e,t,r);case"base64":return A(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var P=4096;function O(e){var t=e.length;if(t<=P)return String.fromCharCode.apply(String,e);var r="",n=0;while(n<t)r+=String.fromCharCode.apply(String,e.slice(n,n+=P));return r}function U(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function k(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function M(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=Z(e[o]);return i}function D(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function V(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function q(e,t,r,n,i,o){if(!f.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function F(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function j(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function Y(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function H(e,t,r,n,o){return o||Y(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function G(e,t,r,n,o){return o||Y(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}f.prototype.slice=function(e,t){var r,n=this.length;if(e=~~e,t=void 0===t?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e),f.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=f.prototype;else{var i=t-e;r=new f(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},f.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||V(e,t,this.length);var n=this[e],i=1,o=0;while(++o<t&&(i*=256))n+=this[e+o]*i;return n},f.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||V(e,t,this.length);var n=this[e+--t],i=1;while(t>0&&(i*=256))n+=this[e+--t]*i;return n},f.prototype.readUInt8=function(e,t){return t||V(e,1,this.length),this[e]},f.prototype.readUInt16LE=function(e,t){return t||V(e,2,this.length),this[e]|this[e+1]<<8},f.prototype.readUInt16BE=function(e,t){return t||V(e,2,this.length),this[e]<<8|this[e+1]},f.prototype.readUInt32LE=function(e,t){return t||V(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},f.prototype.readUInt32BE=function(e,t){return t||V(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},f.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||V(e,t,this.length);var n=this[e],i=1,o=0;while(++o<t&&(i*=256))n+=this[e+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},f.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||V(e,t,this.length);var n=t,i=1,o=this[e+--n];while(n>0&&(i*=256))o+=this[e+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},f.prototype.readInt8=function(e,t){return t||V(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},f.prototype.readInt16LE=function(e,t){t||V(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(e,t){t||V(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(e,t){return t||V(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},f.prototype.readInt32BE=function(e,t){return t||V(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},f.prototype.readFloatLE=function(e,t){return t||V(e,4,this.length),i.read(this,e,!0,23,4)},f.prototype.readFloatBE=function(e,t){return t||V(e,4,this.length),i.read(this,e,!1,23,4)},f.prototype.readDoubleLE=function(e,t){return t||V(e,8,this.length),i.read(this,e,!0,52,8)},f.prototype.readDoubleBE=function(e,t){return t||V(e,8,this.length),i.read(this,e,!1,52,8)},f.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;q(this,e,t,r,i,0)}var o=1,s=0;this[t]=255&e;while(++s<r&&(o*=256))this[t+s]=e/o&255;return t+r},f.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;q(this,e,t,r,i,0)}var o=r-1,s=1;this[t+o]=255&e;while(--o>=0&&(s*=256))this[t+o]=e/s&255;return t+r},f.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,1,255,0),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},f.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},f.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},f.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):j(this,e,t,!0),t+4},f.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},f.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);q(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;this[t]=255&e;while(++o<r&&(s*=256))e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+r},f.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);q(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;this[t+o]=255&e;while(--o>=0&&(s*=256))e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+r},f.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,1,127,-128),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},f.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},f.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},f.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):j(this,e,t,!0),t+4},f.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||q(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},f.prototype.writeFloatLE=function(e,t,r){return H(this,e,t,!0,r)},f.prototype.writeFloatBE=function(e,t,r){return H(this,e,t,!1,r)},f.prototype.writeDoubleLE=function(e,t,r){return G(this,e,t,!0,r)},f.prototype.writeDoubleBE=function(e,t,r){return G(this,e,t,!1,r)},f.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},f.prototype.fill=function(e,t,r,n){if("string"===typeof e){if("string"===typeof t?(n=t,t=0,r=this.length):"string"===typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!==typeof n)throw new TypeError("encoding must be a string");if("string"===typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"===typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"===typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=f.isBuffer(e)?e:X(new f(e,n).toString()),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var z=/[^+\/0-9A-Za-z-_]/g;function W(e){if(e=K(e).replace(z,""),e.length<2)return"";while(e.length%4!==0)e+="=";return e}function K(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Z(e){return e<16?"0"+e.toString(16):e.toString(16)}function X(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function $(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function Q(e,t){for(var r,n,i,o=[],s=0;s<e.length;++s){if((t-=2)<0)break;r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n)}return o}function J(e){return n.toByteArray(W(e))}function ee(e,t,r,n){for(var i=0;i<n;++i){if(i+r>=t.length||i>=e.length)break;t[i+r]=e[i]}return i}function te(e){return e!==e}}).call(this,"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{"base64-js":29,ieee754:32,isarray:34}],31:[function(e,t,r){(function(r,n){"function"===typeof define&&define["amd"]?define(["Long"],n):"function"===typeof e&&"object"===typeof t&&t&&t["exports"]?t["exports"]=function(){var t;try{t=e("long")}catch(r){}return n(t)}():(r["dcodeIO"]=r["dcodeIO"]||{})["ByteBuffer"]=n(r["dcodeIO"]["Long"])})(this,(function(e){"use strict";var t=function(e,r,i){if("undefined"===typeof e&&(e=t.DEFAULT_CAPACITY),"undefined"===typeof r&&(r=t.DEFAULT_ENDIAN),"undefined"===typeof i&&(i=t.DEFAULT_NOASSERT),!i){if(e|=0,e<0)throw RangeError("Illegal capacity");r=!!r,i=!!i}this.buffer=0===e?n:new ArrayBuffer(e),this.view=0===e?null:new Uint8Array(this.buffer),this.offset=0,this.markedOffset=-1,this.limit=e,this.littleEndian="undefined"!==typeof r&&!!r,this.noAssert=!!i};t.VERSION="4.1.0",t.LITTLE_ENDIAN=!0,t.BIG_ENDIAN=!1,t.DEFAULT_CAPACITY=16,t.DEFAULT_ENDIAN=t.BIG_ENDIAN,t.DEFAULT_NOASSERT=!1,t.Long=e||null;var r=t.prototype;r.__isByteBuffer__,Object.defineProperty(r,"__isByteBuffer__",{value:!0,enumerable:!1,configurable:!1});var n=new ArrayBuffer(0),i=String.fromCharCode;function o(e){var t=0;return function(){return t<e.length?e.charCodeAt(t++):null}}function s(){var e=[],t=[];return function(){if(0===arguments.length)return t.join("")+i.apply(String,e);e.length+arguments.length>1024&&(t.push(i.apply(String,e)),e.length=0),Array.prototype.push.apply(e,arguments)}}function a(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,l=-7,h=r?i-1:0,c=r?-1:1,p=e[t+h];for(h+=c,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+e[t+h],h+=c,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+h],h+=c,l-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=f}return(p?-1:1)*s*Math.pow(2,o-n)}function u(e,t,r,n,i,o){var s,a,u,f=8*o-i-1,l=(1<<f)-1,h=l>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+h>=1?c/u:c*Math.pow(2,1-h),t*u>=2&&(s++,u/=2),s+h>=l?(a=0,s=l):s+h>=1?(a=(t*u-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+p]=255&s,p+=d,s/=256,f-=8);e[r+p-d]|=128*y}t.accessor=function(){return Uint8Array},t.allocate=function(e,r,n){return new t(e,r,n)},t.concat=function(e,r,n,i){"boolean"!==typeof r&&"string"===typeof r||(i=n,n=r,r=void 0);for(var o,s=0,a=0,u=e.length;a<u;++a)t.isByteBuffer(e[a])||(e[a]=t.wrap(e[a],r)),o=e[a].limit-e[a].offset,o>0&&(s+=o);if(0===s)return new t(0,n,i);var f,l=new t(s,n,i);a=0;while(a<u)f=e[a++],o=f.limit-f.offset,o<=0||(l.view.set(f.view.subarray(f.offset,f.limit),l.offset),l.offset+=o);return l.limit=l.offset,l.offset=0,l},t.isByteBuffer=function(e){return!0===(e&&e["__isByteBuffer__"])},t.type=function(){return ArrayBuffer},t.wrap=function(e,n,i,o){if("string"!==typeof n&&(o=i,i=n,n=void 0),"string"===typeof e)switch("undefined"===typeof n&&(n="utf8"),n){case"base64":return t.fromBase64(e,i);case"hex":return t.fromHex(e,i);case"binary":return t.fromBinary(e,i);case"utf8":return t.fromUTF8(e,i);case"debug":return t.fromDebug(e,i);default:throw Error("Unsupported encoding: "+n)}if(null===e||"object"!==typeof e)throw TypeError("Illegal buffer");var s;if(t.isByteBuffer(e))return s=r.clone.call(e),s.markedOffset=-1,s;if(e instanceof Uint8Array)s=new t(0,i,o),e.length>0&&(s.buffer=e.buffer,s.offset=e.byteOffset,s.limit=e.byteOffset+e.byteLength,s.view=new Uint8Array(e.buffer));else if(e instanceof ArrayBuffer)s=new t(0,i,o),e.byteLength>0&&(s.buffer=e,s.offset=0,s.limit=e.byteLength,s.view=e.byteLength>0?new Uint8Array(e):null);else{if("[object Array]"!==Object.prototype.toString.call(e))throw TypeError("Illegal buffer");s=new t(e.length,i,o),s.limit=e.length;for(var a=0;a<e.length;++a)s.view[a]=e[a]}return s},r.readBytes=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+"+e+") <= "+this.buffer.byteLength)}var n=this.slice(t,t+e);return r&&(this.offset+=e),n},r.writeBytes=r.append,r.writeInt8=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeByte=r.writeInt8,r.readInt8=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return 128===(128&r)&&(r=-(255-r+1)),t&&(this.offset+=1),r},r.readByte=r.readInt8,r.writeUint8=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeUInt8=r.writeUint8,r.readUint8=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return t&&(this.offset+=1),r},r.readUInt8=r.readUint8,r.writeInt16=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeShort=r.writeInt16,r.readInt16=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),32768===(32768&r)&&(r=-(65535-r+1)),t&&(this.offset+=2),r},r.readShort=r.readInt16,r.writeUint16=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeUInt16=r.writeUint16,r.readUint16=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),t&&(this.offset+=2),r},r.readUInt16=r.readUint16,r.writeInt32=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeInt=r.writeInt32,r.readInt32=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),r|=0,t&&(this.offset+=4),r},r.readInt=r.readInt32,r.writeUint32=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeUInt32=r.writeUint32,r.readUint32=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),t&&(this.offset+=4),r},r.readUInt32=r.readUint32,e&&(r.writeInt64=function(t,r){var n="undefined"===typeof r;if(n&&(r=this.offset),!this.noAssert){if("number"===typeof t)t=e.fromNumber(t);else if("string"===typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!==typeof r||r%1!==0)throw TypeError("Illegal offset: "+r+" (not an integer)");if(r>>>=0,r<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"===typeof t?t=e.fromNumber(t):"string"===typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeLong=r.writeInt64,r.readInt64=function(t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!1);return r&&(this.offset+=8),o},r.readLong=r.readInt64,r.writeUint64=function(t,r){var n="undefined"===typeof r;if(n&&(r=this.offset),!this.noAssert){if("number"===typeof t)t=e.fromNumber(t);else if("string"===typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!==typeof r||r%1!==0)throw TypeError("Illegal offset: "+r+" (not an integer)");if(r>>>=0,r<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"===typeof t?t=e.fromNumber(t):"string"===typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeUInt64=r.writeUint64,r.readUint64=function(t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!0);return r&&(this.offset+=8),o},r.readUInt64=r.readUint64),r.writeFloat32=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,u(this.view,e,t,this.littleEndian,23,4),r&&(this.offset+=4),this},r.writeFloat=r.writeFloat32,r.readFloat32=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,23,4);return t&&(this.offset+=4),r},r.readFloat=r.readFloat32,r.writeFloat64=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=8;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=8,u(this.view,e,t,this.littleEndian,52,8),r&&(this.offset+=8),this},r.writeDouble=r.writeFloat64,r.readFloat64=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+8) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,52,8);return t&&(this.offset+=8),r},r.readDouble=r.readFloat64,t.MAX_VARINT32_BYTES=5,t.calculateVarint32=function(e){return e>>>=0,e<128?1:e<16384?2:e<1<<21?3:e<1<<28?4:5},t.zigZagEncode32=function(e){return((e|=0)<<1^e>>31)>>>0},t.zigZagDecode32=function(e){return e>>>1^-(1&e)},r.writeVarint32=function(e,r){var n="undefined"===typeof r;if(n&&(r=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!==typeof r||r%1!==0)throw TypeError("Illegal offset: "+r+" (not an integer)");if(r>>>=0,r<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,o=t.calculateVarint32(e);r+=o;var s=this.buffer.byteLength;r>s&&this.resize((s*=2)>r?s:r),r-=o,e>>>=0;while(e>=128)i=127&e|128,this.view[r++]=i,e>>>=7;return this.view[r++]=e,n?(this.offset=r,this):o},r.writeVarint32ZigZag=function(e,r){return this.writeVarint32(t.zigZagEncode32(e),r)},r.readVarint32=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=0,i=0;do{if(!this.noAssert&&e>this.limit){var o=Error("Truncated");throw o["truncated"]=!0,o}r=this.view[e++],n<5&&(i|=(127&r)<<7*n),++n}while(0!==(128&r));return i|=0,t?(this.offset=e,i):{value:i,length:n}},r.readVarint32ZigZag=function(e){var r=this.readVarint32(e);return"object"===typeof r?r["value"]=t.zigZagDecode32(r["value"]):r=t.zigZagDecode32(r),r},e&&(t.MAX_VARINT64_BYTES=10,t.calculateVarint64=function(t){"number"===typeof t?t=e.fromNumber(t):"string"===typeof t&&(t=e.fromString(t));var r=t.toInt()>>>0,n=t.shiftRightUnsigned(28).toInt()>>>0,i=t.shiftRightUnsigned(56).toInt()>>>0;return 0==i?0==n?r<16384?r<128?1:2:r<1<<21?3:4:n<16384?n<128?5:6:n<1<<21?7:8:i<128?9:10},t.zigZagEncode64=function(t){return"number"===typeof t?t=e.fromNumber(t,!1):"string"===typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftLeft(1).xor(t.shiftRight(63)).toUnsigned()},t.zigZagDecode64=function(t){return"number"===typeof t?t=e.fromNumber(t,!1):"string"===typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftRightUnsigned(1).xor(t.and(e.ONE).toSigned().negate()).toSigned()},r.writeVarint64=function(r,n){var i="undefined"===typeof n;if(i&&(n=this.offset),!this.noAssert){if("number"===typeof r)r=e.fromNumber(r);else if("string"===typeof r)r=e.fromString(r);else if(!(r&&r instanceof e))throw TypeError("Illegal value: "+r+" (not an integer or Long)");if("number"!==typeof n||n%1!==0)throw TypeError("Illegal offset: "+n+" (not an integer)");if(n>>>=0,n<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}"number"===typeof r?r=e.fromNumber(r,!1):"string"===typeof r?r=e.fromString(r,!1):!1!==r.unsigned&&(r=r.toSigned());var o=t.calculateVarint64(r),s=r.toInt()>>>0,a=r.shiftRightUnsigned(28).toInt()>>>0,u=r.shiftRightUnsigned(56).toInt()>>>0;n+=o;var f=this.buffer.byteLength;switch(n>f&&this.resize((f*=2)>n?f:n),n-=o,o){case 10:this.view[n+9]=u>>>7&1;case 9:this.view[n+8]=9!==o?128|u:127&u;case 8:this.view[n+7]=8!==o?a>>>21|128:a>>>21&127;case 7:this.view[n+6]=7!==o?a>>>14|128:a>>>14&127;case 6:this.view[n+5]=6!==o?a>>>7|128:a>>>7&127;case 5:this.view[n+4]=5!==o?128|a:127&a;case 4:this.view[n+3]=4!==o?s>>>21|128:s>>>21&127;case 3:this.view[n+2]=3!==o?s>>>14|128:s>>>14&127;case 2:this.view[n+1]=2!==o?s>>>7|128:s>>>7&127;case 1:this.view[n]=1!==o?128|s:127&s}return i?(this.offset+=o,this):o},r.writeVarint64ZigZag=function(e,r){return this.writeVarint64(t.zigZagEncode64(e),r)},r.readVarint64=function(t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+1) <= "+this.buffer.byteLength)}var n=t,i=0,o=0,s=0,a=0;if(a=this.view[t++],i=127&a,128&a&&(a=this.view[t++],i|=(127&a)<<7,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],i|=(127&a)<<14,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],i|=(127&a)<<21,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],o=127&a,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],o|=(127&a)<<7,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],o|=(127&a)<<14,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],o|=(127&a)<<21,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],s=127&a,(128&a||this.noAssert&&"undefined"===typeof a)&&(a=this.view[t++],s|=(127&a)<<7,128&a||this.noAssert&&"undefined"===typeof a))))))))))throw Error("Buffer overrun");var u=e.fromBits(i|o<<28,o>>>4|s<<24,!1);return r?(this.offset=t,u):{value:u,length:t-n}},r.readVarint64ZigZag=function(r){var n=this.readVarint64(r);return n&&n["value"]instanceof e?n["value"]=t.zigZagDecode64(n["value"]):n=t.zigZagDecode64(n),n}),r.writeCString=function(e,t){var r="undefined"===typeof t;r&&(t=this.offset);var n,i=e.length;if(!this.noAssert){if("string"!==typeof e)throw TypeError("Illegal str: Not a string");for(n=0;n<i;++n)if(0===e.charCodeAt(n))throw RangeError("Illegal str: Contains NULL-characters");if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}i=l.calculateUTF16asUTF8(o(e))[1],t+=i+1;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=i+1,l.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),this.view[t++]=0,r?(this.offset=t,this):i},r.readCString=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=e,i=-1;return l.decodeUTF8toUTF16(function(){if(0===i)return null;if(e>=this.limit)throw RangeError("Illegal range: Truncated data, "+e+" < "+this.limit);return i=this.view[e++],0===i?null:i}.bind(this),r=s(),!0),t?(this.offset=e,r()):{string:r(),length:e-n}},r.writeIString=function(e,t){var r="undefined"===typeof t;if(r&&(t=this.offset),!this.noAssert){if("string"!==typeof e)throw TypeError("Illegal str: Not a string");if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n,i=t;n=l.calculateUTF16asUTF8(o(e),this.noAssert)[1],t+=4+n;var s=this.buffer.byteLength;if(t>s&&this.resize((s*=2)>t?s:t),t-=4+n,this.littleEndian?(this.view[t+3]=n>>>24&255,this.view[t+2]=n>>>16&255,this.view[t+1]=n>>>8&255,this.view[t]=255&n):(this.view[t]=n>>>24&255,this.view[t+1]=n>>>16&255,this.view[t+2]=n>>>8&255,this.view[t+3]=255&n),t+=4,l.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),t!==i+4+n)throw RangeError("Illegal range: Truncated data, "+t+" == "+(t+4+n));return r?(this.offset=t,this):t-i},r.readIString=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r,n=0,i=e;this.littleEndian?(n=this.view[e+2]<<16,n|=this.view[e+1]<<8,n|=this.view[e],n+=this.view[e+3]<<24>>>0):(n=this.view[e+1]<<16,n|=this.view[e+2]<<8,n|=this.view[e+3],n+=this.view[e]<<24>>>0),e+=4;var o,a=e+n;return l.decodeUTF8toUTF16(function(){return e<a?this.view[e++]:null}.bind(this),o=s(),this.noAssert),r=o(),t?(this.offset=e,r):{string:r,length:e-i}},t.METRICS_CHARS="c",t.METRICS_BYTES="b",r.writeUTF8String=function(e,t){var r,n="undefined"===typeof t;if(n&&(t=this.offset),!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: "+t+" (not an integer)");if(t>>>=0,t<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var i=t;r=l.calculateUTF16asUTF8(o(e))[1],t+=r;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=r,l.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),n?(this.offset=t,this):t-i},r.writeString=r.writeUTF8String,t.calculateUTF8Chars=function(e){return l.calculateUTF16asUTF8(o(e))[0]},t.calculateUTF8Bytes=function(e){return l.calculateUTF16asUTF8(o(e))[1]},t.calculateString=t.calculateUTF8Bytes,r.readUTF8String=function(e,r,n){"number"===typeof r&&(n=r,r=void 0);var i="undefined"===typeof n;if(i&&(n=this.offset),"undefined"===typeof r&&(r=t.METRICS_CHARS),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal length: "+e+" (not an integer)");if(e|=0,"number"!==typeof n||n%1!==0)throw TypeError("Illegal offset: "+n+" (not an integer)");if(n>>>=0,n<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}var o,a=0,u=n;if(r===t.METRICS_CHARS){if(o=s(),l.decodeUTF8(function(){return a<e&&n<this.limit?this.view[n++]:null}.bind(this),(function(e){++a,l.UTF8toUTF16(e,o)})),a!==e)throw RangeError("Illegal range: Truncated data, "+a+" == "+e);return i?(this.offset=n,o()):{string:o(),length:n-u}}if(r===t.METRICS_BYTES){if(!this.noAssert){if("number"!==typeof n||n%1!==0)throw TypeError("Illegal offset: "+n+" (not an integer)");if(n>>>=0,n<0||n+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+"+e+") <= "+this.buffer.byteLength)}var f=n+e;if(l.decodeUTF8toUTF16(function(){return n<f?this.view[n++]:null}.bind(this),o=s(),this.noAssert),n!==f)throw RangeError("Illegal range: Truncated data, "+n+" == "+f);return i?(this.offset=n,o()):{string:o(),length:n-u}}throw TypeError("Unsupported metrics: "+r)},r.readString=r.readUTF8String,r.writeVString=function(e,r){var n="undefined"===typeof r;if(n&&(r=this.offset),!this.noAssert){if("string"!==typeof e)throw TypeError("Illegal str: Not a string");if("number"!==typeof r||r%1!==0)throw TypeError("Illegal offset: "+r+" (not an integer)");if(r>>>=0,r<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,s,a=r;i=l.calculateUTF16asUTF8(o(e),this.noAssert)[1],s=t.calculateVarint32(i),r+=s+i;var u=this.buffer.byteLength;if(r>u&&this.resize((u*=2)>r?u:r),r-=s+i,r+=this.writeVarint32(i,r),l.encodeUTF16toUTF8(o(e),function(e){this.view[r++]=e}.bind(this)),r!==a+i+s)throw RangeError("Illegal range: Truncated data, "+r+" == "+(r+i+s));return n?(this.offset=r,this):r-a},r.readVString=function(e){var t="undefined"===typeof e;if(t&&(e=this.offset),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=this.readVarint32(e),i=e;e+=n["length"],n=n["value"];var o=e+n,a=s();return l.decodeUTF8toUTF16(function(){return e<o?this.view[e++]:null}.bind(this),a,this.noAssert),r=a(),t?(this.offset=e,r):{string:r,length:e-i}},r.append=function(e,r,n){"number"!==typeof r&&"string"===typeof r||(n=r,r=void 0);var i="undefined"===typeof n;if(i&&(n=this.offset),!this.noAssert){if("number"!==typeof n||n%1!==0)throw TypeError("Illegal offset: "+n+" (not an integer)");if(n>>>=0,n<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;n+=o;var s=this.buffer.byteLength;return n>s&&this.resize((s*=2)>n?s:n),n-=o,this.view.set(e.view.subarray(e.offset,e.limit),n),e.offset+=o,i&&(this.offset+=o),this},r.appendTo=function(e,t){return e.append(this,t),this},r.assert=function(e){return this.noAssert=!e,this},r.capacity=function(){return this.buffer.byteLength},r.clear=function(){return this.offset=0,this.limit=this.buffer.byteLength,this.markedOffset=-1,this},r.clone=function(e){var r=new t(0,this.littleEndian,this.noAssert);return e?(r.buffer=new ArrayBuffer(this.buffer.byteLength),r.view=new Uint8Array(r.buffer)):(r.buffer=this.buffer,r.view=this.view),r.offset=this.offset,r.markedOffset=this.markedOffset,r.limit=this.limit,r},r.compact=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}if(0===e&&t===this.buffer.byteLength)return this;var r=t-e;if(0===r)return this.buffer=n,this.view=null,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=0,this;var i=new ArrayBuffer(r),o=new Uint8Array(i);return o.set(this.view.subarray(e,t)),this.buffer=i,this.view=o,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=r,this},r.copy=function(e,r){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof r&&(r=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof r||r%1!==0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,e<0||e>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+r+" <= "+this.buffer.byteLength)}if(e===r)return new t(0,this.littleEndian,this.noAssert);var n=r-e,i=new t(n,this.littleEndian,this.noAssert);return i.offset=0,i.limit=n,i.markedOffset>=0&&(i.markedOffset-=e),this.copyTo(i,0,e,r),i},r.copyTo=function(e,r,n,i){var o,s;if(!this.noAssert&&!t.isByteBuffer(e))throw TypeError("Illegal target: Not a ByteBuffer");if(r=(s="undefined"===typeof r)?e.offset:0|r,n=(o="undefined"===typeof n)?this.offset:0|n,i="undefined"===typeof i?this.limit:0|i,r<0||r>e.buffer.byteLength)throw RangeError("Illegal target range: 0 <= "+r+" <= "+e.buffer.byteLength);if(n<0||i>this.buffer.byteLength)throw RangeError("Illegal source range: 0 <= "+n+" <= "+this.buffer.byteLength);var a=i-n;return 0===a?e:(e.ensureCapacity(r+a),e.view.set(this.view.subarray(n,i),r),o&&(this.offset+=a),s&&(e.offset+=a),this)},r.ensureCapacity=function(e){var t=this.buffer.byteLength;return t<e?this.resize((t*=2)>e?t:e):this},r.fill=function(e,t,r){var n="undefined"===typeof t;if(n&&(t=this.offset),"string"===typeof e&&e.length>0&&(e=e.charCodeAt(0)),"undefined"===typeof t&&(t=this.offset),"undefined"===typeof r&&(r=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal begin: Not an integer");if(t>>>=0,"number"!==typeof r||r%1!==0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(t>=r)return this;while(t<r)this.view[t++]=e;return n&&(this.offset=t),this},r.flip=function(){return this.limit=this.offset,this.offset=0,this},r.mark=function(e){if(e="undefined"===typeof e?this.offset:e,!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal offset: "+e+" (not an integer)");if(e>>>=0,e<0||e+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+0) <= "+this.buffer.byteLength)}return this.markedOffset=e,this},r.order=function(e){if(!this.noAssert&&"boolean"!==typeof e)throw TypeError("Illegal littleEndian: Not a boolean");return this.littleEndian=!!e,this},r.LE=function(e){return this.littleEndian="undefined"===typeof e||!!e,this},r.BE=function(e){return this.littleEndian="undefined"!==typeof e&&!e,this},r.prepend=function(e,r,n){"number"!==typeof r&&"string"===typeof r||(n=r,r=void 0);var i="undefined"===typeof n;if(i&&(n=this.offset),!this.noAssert){if("number"!==typeof n||n%1!==0)throw TypeError("Illegal offset: "+n+" (not an integer)");if(n>>>=0,n<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;var s=o-n;if(s>0){var a=new ArrayBuffer(this.buffer.byteLength+s),u=new Uint8Array(a);u.set(this.view.subarray(n,this.buffer.byteLength),o),this.buffer=a,this.view=u,this.offset+=s,this.markedOffset>=0&&(this.markedOffset+=s),this.limit+=s,n+=s}else new Uint8Array(this.buffer);return this.view.set(e.view.subarray(e.offset,e.limit),n-o),e.offset=e.limit,i&&(this.offset-=o),this},r.prependTo=function(e,t){return e.prepend(this,t),this},r.printDebug=function(e){"function"!==typeof e&&(e=console.log.bind(console)),e(this.toString()+"\n-------------------------------------------------------------------\n"+this.toDebug(!0))},r.remaining=function(){return this.limit-this.offset},r.reset=function(){return this.markedOffset>=0?(this.offset=this.markedOffset,this.markedOffset=-1):this.offset=0,this},r.resize=function(e){if(!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal capacity: "+e+" (not an integer)");if(e|=0,e<0)throw RangeError("Illegal capacity: 0 <= "+e)}if(this.buffer.byteLength<e){var t=new ArrayBuffer(e),r=new Uint8Array(t);r.set(this.view),this.buffer=t,this.view=r}return this},r.reverse=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}return e===t||Array.prototype.reverse.call(this.view.subarray(e,t)),this},r.skip=function(e){if(!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal length: "+e+" (not an integer)");e|=0}var t=this.offset+e;if(!this.noAssert&&(t<0||t>this.buffer.byteLength))throw RangeError("Illegal length: 0 <= "+this.offset+" + "+e+" <= "+this.buffer.byteLength);return this.offset=t,this},r.slice=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r=this.clone();return r.offset=e,r.limit=t,r},r.toBuffer=function(e){var t=this.offset,r=this.limit;if(!this.noAssert){if("number"!==typeof t||t%1!==0)throw TypeError("Illegal offset: Not an integer");if(t>>>=0,"number"!==typeof r||r%1!==0)throw TypeError("Illegal limit: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(!e&&0===t&&r===this.buffer.byteLength)return this.buffer;if(t===r)return n;var i=new ArrayBuffer(r-t);return new Uint8Array(i).set(new Uint8Array(this.buffer).subarray(t,r),0),i},r.toArrayBuffer=r.toBuffer,r.toString=function(e,t,r){if("undefined"===typeof e)return"ByteBufferAB(offset="+this.offset+",markedOffset="+this.markedOffset+",limit="+this.limit+",capacity="+this.capacity()+")";switch("number"===typeof e&&(e="utf8",t=e,r=t),e){case"utf8":return this.toUTF8(t,r);case"base64":return this.toBase64(t,r);case"hex":return this.toHex(t,r);case"binary":return this.toBinary(t,r);case"debug":return this.toDebug();case"columns":return this.toColumns();default:throw Error("Unsupported encoding: "+e)}};var f=function(){for(var e={},t=[65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,48,49,50,51,52,53,54,55,56,57,43,47],r=[],n=0,i=t.length;n<i;++n)r[t[n]]=n;return e.encode=function(e,r){var n,i;while(null!==(n=e()))r(t[n>>2&63]),i=(3&n)<<4,null!==(n=e())?(i|=n>>4&15,r(t[63&(i|n>>4&15)]),i=(15&n)<<2,null!==(n=e())?(r(t[63&(i|n>>6&3)]),r(t[63&n])):(r(t[63&i]),r(61))):(r(t[63&i]),r(61),r(61))},e.decode=function(e,t){var n,i,o;function s(e){throw Error("Illegal character code: "+e)}while(null!==(n=e()))if(i=r[n],"undefined"===typeof i&&s(n),null!==(n=e())&&(o=r[n],"undefined"===typeof o&&s(n),t(i<<2>>>0|(48&o)>>4),null!==(n=e()))){if(i=r[n],"undefined"===typeof i){if(61===n)break;s(n)}if(t((15&o)<<4>>>0|(60&i)>>2),null!==(n=e())){if(o=r[n],"undefined"===typeof o){if(61===n)break;s(n)}t((3&i)<<6>>>0|o)}}},e.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)},e}();r.toBase64=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),e|=0,t|=0,e<0||t>this.capacity||e>t)throw RangeError("begin, end");var r;return f.encode(function(){return e<t?this.view[e++]:null}.bind(this),r=s()),r()},t.fromBase64=function(e,r){if("string"!==typeof e)throw TypeError("str");var n=new t(e.length/4*3,r),i=0;return f.decode(o(e),(function(e){n.view[i++]=e})),n.limit=i,n},t.btoa=function(e){return t.fromBinary(e).toBase64()},t.atob=function(e){return t.fromBase64(e).toBinary()},r.toBinary=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),e|=0,t|=0,e<0||t>this.capacity()||e>t)throw RangeError("begin, end");if(e===t)return"";var r=[],n=[];while(e<t)r.push(this.view[e++]),r.length>=1024&&(n.push(String.fromCharCode.apply(String,r)),r=[]);return n.join("")+String.fromCharCode.apply(String,r)},t.fromBinary=function(e,r){if("string"!==typeof e)throw TypeError("str");var n,i=0,o=e.length,s=new t(o,r);while(i<o){if(n=e.charCodeAt(i),n>255)throw RangeError("illegal char code: "+n);s.view[i++]=n}return s.limit=o,s},r.toDebug=function(e){var t,r=-1,n=this.buffer.byteLength,i="",o="",s="";while(r<n){if(-1!==r&&(t=this.view[r],i+=t<16?"0"+t.toString(16).toUpperCase():t.toString(16).toUpperCase(),e&&(o+=t>32&&t<127?String.fromCharCode(t):".")),++r,e&&r>0&&r%16===0&&r!==n){while(i.length<51)i+=" ";s+=i+o+"\n",i=o=""}r===this.offset&&r===this.limit?i+=r===this.markedOffset?"!":"|":r===this.offset?i+=r===this.markedOffset?"[":"<":r===this.limit?i+=r===this.markedOffset?"]":">":i+=r===this.markedOffset?"'":e||0!==r&&r!==n?" ":""}if(e&&" "!==i){while(i.length<51)i+=" ";s+=i+o+"\n"}return e?s:i},t.fromDebug=function(e,r,n){var i,o,s=e.length,a=new t((s+1)/3|0,r,n),u=0,f=0,l=!1,h=!1,c=!1,p=!1,d=!1;while(u<s){switch(i=e.charAt(u++)){case"!":if(!n){if(h||c||p){d=!0;break}h=c=p=!0}a.offset=a.markedOffset=a.limit=f,l=!1;break;case"|":if(!n){if(h||p){d=!0;break}h=p=!0}a.offset=a.limit=f,l=!1;break;case"[":if(!n){if(h||c){d=!0;break}h=c=!0}a.offset=a.markedOffset=f,l=!1;break;case"<":if(!n){if(h){d=!0;break}h=!0}a.offset=f,l=!1;break;case"]":if(!n){if(p||c){d=!0;break}p=c=!0}a.limit=a.markedOffset=f,l=!1;break;case">":if(!n){if(p){d=!0;break}p=!0}a.limit=f,l=!1;break;case"'":if(!n){if(c){d=!0;break}c=!0}a.markedOffset=f,l=!1;break;case" ":l=!1;break;default:if(!n&&l){d=!0;break}if(o=parseInt(i+e.charAt(u++),16),!n&&(isNaN(o)||o<0||o>255))throw TypeError("Illegal str: Not a debug encoded string");a.view[f++]=o,l=!0}if(d)throw TypeError("Illegal str: Invalid symbol at "+u)}if(!n){if(!h||!p)throw TypeError("Illegal str: Missing offset or limit");if(f<a.buffer.byteLength)throw TypeError("Illegal str: Not a debug encoded string (is it hex?) "+f+" < "+s)}return a},r.toHex=function(e,t){if(e="undefined"===typeof e?this.offset:e,t="undefined"===typeof t?this.limit:t,!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r,n=new Array(t-e);while(e<t)r=this.view[e++],r<16?n.push("0",r.toString(16)):n.push(r.toString(16));return n.join("")},t.fromHex=function(e,r,n){if(!n){if("string"!==typeof e)throw TypeError("Illegal str: Not a string");if(e.length%2!==0)throw TypeError("Illegal str: Length not a multiple of 2")}for(var i,o=e.length,s=new t(o/2|0,r),a=0,u=0;a<o;a+=2){if(i=parseInt(e.substring(a,a+2),16),!n&&(!isFinite(i)||i<0||i>255))throw TypeError("Illegal str: Contains non-hex characters");s.view[u++]=i}return s.limit=u,s};var l=function(){var e={MAX_CODEPOINT:1114111,encodeUTF8:function(e,t){var r=null;"number"===typeof e&&(r=e,e=function(){return null});while(null!==r||null!==(r=e()))r<128?t(127&r):r<2048?(t(r>>6&31|192),t(63&r|128)):r<65536?(t(r>>12&15|224),t(r>>6&63|128),t(63&r|128)):(t(r>>18&7|240),t(r>>12&63|128),t(r>>6&63|128),t(63&r|128)),r=null},decodeUTF8:function(e,t){var r,n,i,o,s=function(e){e=e.slice(0,e.indexOf(null));var t=Error(e.toString());throw t.name="TruncatedError",t["bytes"]=e,t};while(null!==(r=e()))if(0===(128&r))t(r);else if(192===(224&r))null===(n=e())&&s([r,n]),t((31&r)<<6|63&n);else if(224===(240&r))(null===(n=e())||null===(i=e()))&&s([r,n,i]),t((15&r)<<12|(63&n)<<6|63&i);else{if(240!==(248&r))throw RangeError("Illegal starting byte: "+r);(null===(n=e())||null===(i=e())||null===(o=e()))&&s([r,n,i,o]),t((7&r)<<18|(63&n)<<12|(63&i)<<6|63&o)}},UTF16toUTF8:function(e,t){var r,n=null;while(1){if(null===(r=null!==n?n:e()))break;r>=55296&&r<=57343&&null!==(n=e())&&n>=56320&&n<=57343?(t(1024*(r-55296)+n-56320+65536),n=null):t(r)}null!==n&&t(n)},UTF8toUTF16:function(e,t){var r=null;"number"===typeof e&&(r=e,e=function(){return null});while(null!==r||null!==(r=e()))r<=65535?t(r):(r-=65536,t(55296+(r>>10)),t(r%1024+56320)),r=null},encodeUTF16toUTF8:function(t,r){e.UTF16toUTF8(t,(function(t){e.encodeUTF8(t,r)}))},decodeUTF8toUTF16:function(t,r){e.decodeUTF8(t,(function(t){e.UTF8toUTF16(t,r)}))},calculateCodePoint:function(e){return e<128?1:e<2048?2:e<65536?3:4},calculateUTF8:function(e){var t,r=0;while(null!==(t=e()))r+=t<128?1:t<2048?2:t<65536?3:4;return r},calculateUTF16asUTF8:function(t){var r=0,n=0;return e.UTF16toUTF8(t,(function(e){++r,n+=e<128?1:e<2048?2:e<65536?3:4})),[r,n]}};return e}();return r.toUTF8=function(e,t){if("undefined"===typeof e&&(e=this.offset),"undefined"===typeof t&&(t=this.limit),!this.noAssert){if("number"!==typeof e||e%1!==0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!==typeof t||t%1!==0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r;try{l.decodeUTF8toUTF16(function(){return e<t?this.view[e++]:null}.bind(this),r=s())}catch(n){if(e!==t)throw RangeError("Illegal range: Truncated data, "+e+" != "+t)}return r()},t.fromUTF8=function(e,r,n){if(!n&&"string"!==typeof e)throw TypeError("Illegal str: Not a string");var i=new t(l.calculateUTF16asUTF8(o(e),!0)[1],r,n),s=0;return l.encodeUTF16toUTF8(o(e),(function(e){i.view[s++]=e})),i.limit=s,i},t}))},{long:35}],32:[function(e,t,r){r.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,l=-7,h=r?i-1:0,c=r?-1:1,p=e[t+h];for(h+=c,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+e[t+h],h+=c,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+h],h+=c,l-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=f}return(p?-1:1)*s*Math.pow(2,o-n)},r.write=function(e,t,r,n,i,o){var s,a,u,f=8*o-i-1,l=(1<<f)-1,h=l>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+h>=1?c/u:c*Math.pow(2,1-h),t*u>=2&&(s++,u/=2),s+h>=l?(a=0,s=l):s+h>=1?(a=(t*u-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+p]=255&s,p+=d,s/=256,f-=8);e[r+p-d]|=128*y}},{}],33:[function(e,t,r){(function(e){!function(t){var r,n="undefined",i=n!==typeof e&&e,o=n!==typeof Uint8Array&&Uint8Array,s=n!==typeof ArrayBuffer&&ArrayBuffer,a=[0,0,0,0,0,0,0,0],u=Array.isArray||T,f=4294967296,l=16777216;function h(e,u,h){var T=u?0:4,_=u?4:0,S=u?0:3,R=u?1:2,C=u?2:1,L=u?3:0,I=u?w:v,A=u?b:E,B=P.prototype,x="is"+e,N="_"+x;return B.buffer=void 0,B.offset=0,B[N]=!0,B.toNumber=M,B.toString=D,B.toJSON=M,B.toArray=c,i&&(B.toBuffer=p),o&&(B.toArrayBuffer=d),P[x]=O,t[e]=P,P;function P(e,t,r,n){return this instanceof P?U(this,e,t,r,n):new P(e,t,r,n)}function O(e){return!(!e||!e[N])}function U(e,t,i,u,f){if(o&&s&&(t instanceof s&&(t=new o(t)),u instanceof s&&(u=new o(u))),t||i||u||r){if(!y(t,i)){var l=r||Array;f=i,u=t,i=0,t=new l(8)}e.buffer=t,e.offset=i|=0,n!==typeof u&&("string"===typeof u?k(t,i,u,f||10):y(u,f)?g(t,i,u,f):"number"===typeof f?(V(t,i+T,u),V(t,i+_,f)):u>0?I(t,i,u):u<0?A(t,i,u):g(t,i,a,0))}else e.buffer=m(a,0)}function k(e,t,r,n){var i=0,o=r.length,s=0,a=0;"-"===r[0]&&i++;var u=i;while(i<o){var l=parseInt(r[i++],n);if(!(l>=0))break;a=a*n+l,s=s*n+Math.floor(a/f),a%=f}u&&(s=~s,a?a=f-a:s++),V(e,t+T,s),V(e,t+_,a)}function M(){var e=this.buffer,t=this.offset,r=q(e,t+T),n=q(e,t+_);return h||(r|=0),r?r*f+n:n}function D(e){var t=this.buffer,r=this.offset,n=q(t,r+T),i=q(t,r+_),o="",s=!h&&2147483648&n;s&&(n=~n,i=f-i),e=e||10;while(1){var a=n%e*f+i;if(n=Math.floor(n/e),i=Math.floor(a/e),o=(a%e).toString(e)+o,!n&&!i)break}return s&&(o="-"+o),o}function V(e,t,r){e[t+L]=255&r,r>>=8,e[t+C]=255&r,r>>=8,e[t+R]=255&r,r>>=8,e[t+S]=255&r}function q(e,t){return e[t+S]*l+(e[t+R]<<16)+(e[t+C]<<8)+e[t+L]}}function c(e){var t=this.buffer,n=this.offset;return r=null,!1!==e&&0===n&&8===t.length&&u(t)?t:m(t,n)}function p(t){var n=this.buffer,o=this.offset;if(r=i,!1!==t&&0===o&&8===n.length&&e.isBuffer(n))return n;var s=new i(8);return g(s,0,n,o),s}function d(e){var t=this.buffer,n=this.offset,i=t.buffer;if(r=o,!1!==e&&0===n&&i instanceof s&&8===i.byteLength)return i;var a=new o(8);return g(a,0,t,n),a.buffer}function y(e,t){var r=e&&e.length;return t|=0,r&&t+8<=r&&"string"!==typeof e[t]}function g(e,t,r,n){t|=0,n|=0;for(var i=0;i<8;i++)e[t++]=255&r[n++]}function m(e,t){return Array.prototype.slice.call(e,t,t+8)}function w(e,t,r){var n=t+8;while(n>t)e[--n]=255&r,r/=256}function b(e,t,r){var n=t+8;r++;while(n>t)e[--n]=255&-r^255,r/=256}function v(e,t,r){var n=t+8;while(t<n)e[t++]=255&r,r/=256}function E(e,t,r){var n=t+8;r++;while(t<n)e[t++]=255&-r^255,r/=256}function T(e){return!!e&&"[object Array]"==Object.prototype.toString.call(e)}h("Uint64BE",!0,!0),h("Int64BE",!0,!1),h("Uint64LE",!1,!0),h("Int64LE",!1,!1)}("object"===typeof r&&"string"!==typeof r.nodeName?r:this||{})}).call(this,e("buffer").Buffer)},{buffer:40}],34:[function(e,t,r){var n={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},{}],35:[function(e,t,r){(function(r,n){"function"===typeof define&&define["amd"]?define([],n):"function"===typeof e&&"object"===typeof t&&t&&t["exports"]?t["exports"]=n():(r["dcodeIO"]=r["dcodeIO"]||{})["Long"]=n()})(this,(function(){"use strict";function e(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}e.__isLong__,Object.defineProperty(e.prototype,"__isLong__",{value:!0,enumerable:!1,configurable:!1}),e.isLong=function(e){return!0===(e&&e["__isLong__"])};var t={},r={};e.fromInt=function(n,i){var o,s;return i?(n>>>=0,0<=n&&n<256&&(s=r[n],s)?s:(o=new e(n,(0|n)<0?-1:0,!0),0<=n&&n<256&&(r[n]=o),o)):(n|=0,-128<=n&&n<128&&(s=t[n],s)?s:(o=new e(n,n<0?-1:0,!1),-128<=n&&n<128&&(t[n]=o),o))},e.fromNumber=function(t,r){return r=!!r,isNaN(t)||!isFinite(t)?e.ZERO:!r&&t<=-a?e.MIN_VALUE:!r&&t+1>=a?e.MAX_VALUE:r&&t>=s?e.MAX_UNSIGNED_VALUE:t<0?e.fromNumber(-t,r).negate():new e(t%o|0,t/o|0,r)},e.fromBits=function(t,r,n){return new e(t,r,n)},e.fromString=function(t,r,n){if(0===t.length)throw Error("number format error: empty string");if("NaN"===t||"Infinity"===t||"+Infinity"===t||"-Infinity"===t)return e.ZERO;if("number"===typeof r&&(n=r,r=!1),n=n||10,n<2||36<n)throw Error("radix out of range: "+n);var i;if((i=t.indexOf("-"))>0)throw Error('number format error: interior "-" character: '+t);if(0===i)return e.fromString(t.substring(1),r,n).negate();for(var o=e.fromNumber(Math.pow(n,8)),s=e.ZERO,a=0;a<t.length;a+=8){var u=Math.min(8,t.length-a),f=parseInt(t.substring(a,a+u),n);if(u<8){var l=e.fromNumber(Math.pow(n,u));s=s.multiply(l).add(e.fromNumber(f))}else s=s.multiply(o),s=s.add(e.fromNumber(f))}return s.unsigned=r,s},e.fromValue=function(t){return t instanceof e?t:"number"===typeof t?e.fromNumber(t):"string"===typeof t?e.fromString(t):new e(t.low,t.high,t.unsigned)};var n=65536,i=1<<24,o=n*n,s=o*o,a=s/2,u=e.fromInt(i);return e.ZERO=e.fromInt(0),e.UZERO=e.fromInt(0,!0),e.ONE=e.fromInt(1),e.UONE=e.fromInt(1,!0),e.NEG_ONE=e.fromInt(-1),e.MAX_VALUE=e.fromBits(-1,2147483647,!1),e.MAX_UNSIGNED_VALUE=e.fromBits(-1,-1,!0),e.MIN_VALUE=e.fromBits(0,-2147483648,!1),e.prototype.toInt=function(){return this.unsigned?this.low>>>0:this.low},e.prototype.toNumber=function(){return this.unsigned?(this.high>>>0)*o+(this.low>>>0):this.high*o+(this.low>>>0)},e.prototype.toString=function(t){if(t=t||10,t<2||36<t)throw RangeError("radix out of range: "+t);if(this.isZero())return"0";var r;if(this.isNegative()){if(this.equals(e.MIN_VALUE)){var n=e.fromNumber(t),i=this.divide(n);return r=i.multiply(n).subtract(this),i.toString(t)+r.toInt().toString(t)}return"-"+this.negate().toString(t)}var o=e.fromNumber(Math.pow(t,6),this.unsigned);r=this;var s="";while(1){var a=r.divide(o),u=r.subtract(a.multiply(o)).toInt()>>>0,f=u.toString(t);if(r=a,r.isZero())return f+s;while(f.length<6)f="0"+f;s=""+f+s}},e.prototype.getHighBits=function(){return this.high},e.prototype.getHighBitsUnsigned=function(){return this.high>>>0},e.prototype.getLowBits=function(){return this.low},e.prototype.getLowBitsUnsigned=function(){return this.low>>>0},e.prototype.getNumBitsAbs=function(){if(this.isNegative())return this.equals(e.MIN_VALUE)?64:this.negate().getNumBitsAbs();for(var t=0!=this.high?this.high:this.low,r=31;r>0;r--)if(0!=(t&1<<r))break;return 0!=this.high?r+33:r+1},e.prototype.isZero=function(){return 0===this.high&&0===this.low},e.prototype.isNegative=function(){return!this.unsigned&&this.high<0},e.prototype.isPositive=function(){return this.unsigned||this.high>=0},e.prototype.isOdd=function(){return 1===(1&this.low)},e.prototype.isEven=function(){return 0===(1&this.low)},e.prototype.equals=function(t){return e.isLong(t)||(t=e.fromValue(t)),(this.unsigned===t.unsigned||this.high>>>31!==1||t.high>>>31!==1)&&(this.high===t.high&&this.low===t.low)},e.eq=e.prototype.equals,e.prototype.notEquals=function(e){return!this.equals(e)},e.neq=e.prototype.notEquals,e.prototype.lessThan=function(e){return this.compare(e)<0},e.prototype.lt=e.prototype.lessThan,e.prototype.lessThanOrEqual=function(e){return this.compare(e)<=0},e.prototype.lte=e.prototype.lessThanOrEqual,e.prototype.greaterThan=function(e){return this.compare(e)>0},e.prototype.gt=e.prototype.greaterThan,e.prototype.greaterThanOrEqual=function(e){return this.compare(e)>=0},e.prototype.gte=e.prototype.greaterThanOrEqual,e.prototype.compare=function(t){if(e.isLong(t)||(t=e.fromValue(t)),this.equals(t))return 0;var r=this.isNegative(),n=t.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.subtract(t).isNegative()?-1:1},e.prototype.negate=function(){return!this.unsigned&&this.equals(e.MIN_VALUE)?e.MIN_VALUE:this.not().add(e.ONE)},e.prototype.neg=e.prototype.negate,e.prototype.add=function(t){e.isLong(t)||(t=e.fromValue(t));var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,s=t.high>>>16,a=65535&t.high,u=t.low>>>16,f=65535&t.low,l=0,h=0,c=0,p=0;return p+=o+f,c+=p>>>16,p&=65535,c+=i+u,h+=c>>>16,c&=65535,h+=n+a,l+=h>>>16,h&=65535,l+=r+s,l&=65535,e.fromBits(c<<16|p,l<<16|h,this.unsigned)},e.prototype.subtract=function(t){return e.isLong(t)||(t=e.fromValue(t)),this.add(t.negate())},e.prototype.sub=e.prototype.subtract,e.prototype.multiply=function(t){if(this.isZero())return e.ZERO;if(e.isLong(t)||(t=e.fromValue(t)),t.isZero())return e.ZERO;if(this.equals(e.MIN_VALUE))return t.isOdd()?e.MIN_VALUE:e.ZERO;if(t.equals(e.MIN_VALUE))return this.isOdd()?e.MIN_VALUE:e.ZERO;if(this.isNegative())return t.isNegative()?this.negate().multiply(t.negate()):this.negate().multiply(t).negate();if(t.isNegative())return this.multiply(t.negate()).negate();if(this.lessThan(u)&&t.lessThan(u))return e.fromNumber(this.toNumber()*t.toNumber(),this.unsigned);var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,s=t.high>>>16,a=65535&t.high,f=t.low>>>16,l=65535&t.low,h=0,c=0,p=0,d=0;return d+=o*l,p+=d>>>16,d&=65535,p+=i*l,c+=p>>>16,p&=65535,p+=o*f,c+=p>>>16,p&=65535,c+=n*l,h+=c>>>16,c&=65535,c+=i*f,h+=c>>>16,c&=65535,c+=o*a,h+=c>>>16,c&=65535,h+=r*l+n*f+i*a+o*s,h&=65535,e.fromBits(p<<16|d,h<<16|c,this.unsigned)},e.prototype.mul=e.prototype.multiply,e.prototype.divide=function(t){if(e.isLong(t)||(t=e.fromValue(t)),t.isZero())throw new Error("division by zero");if(this.isZero())return this.unsigned?e.UZERO:e.ZERO;var r,n,i;if(this.equals(e.MIN_VALUE)){if(t.equals(e.ONE)||t.equals(e.NEG_ONE))return e.MIN_VALUE;if(t.equals(e.MIN_VALUE))return e.ONE;var o=this.shiftRight(1);return r=o.divide(t).shiftLeft(1),r.equals(e.ZERO)?t.isNegative()?e.ONE:e.NEG_ONE:(n=this.subtract(t.multiply(r)),i=r.add(n.divide(t)),i)}if(t.equals(e.MIN_VALUE))return this.unsigned?e.UZERO:e.ZERO;if(this.isNegative())return t.isNegative()?this.negate().divide(t.negate()):this.negate().divide(t).negate();if(t.isNegative())return this.divide(t.negate()).negate();i=e.ZERO,n=this;while(n.greaterThanOrEqual(t)){r=Math.max(1,Math.floor(n.toNumber()/t.toNumber()));var s=Math.ceil(Math.log(r)/Math.LN2),a=s<=48?1:Math.pow(2,s-48),u=e.fromNumber(r),f=u.multiply(t);while(f.isNegative()||f.greaterThan(n))r-=a,u=e.fromNumber(r,this.unsigned),f=u.multiply(t);u.isZero()&&(u=e.ONE),i=i.add(u),n=n.subtract(f)}return i},e.prototype.div=e.prototype.divide,e.prototype.modulo=function(t){return e.isLong(t)||(t=e.fromValue(t)),this.subtract(this.divide(t).multiply(t))},e.prototype.mod=e.prototype.modulo,e.prototype.not=function(){return e.fromBits(~this.low,~this.high,this.unsigned)},e.prototype.and=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low&t.low,this.high&t.high,this.unsigned)},e.prototype.or=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low|t.low,this.high|t.high,this.unsigned)},e.prototype.xor=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low^t.low,this.high^t.high,this.unsigned)},e.prototype.shiftLeft=function(t){return e.isLong(t)&&(t=t.toInt()),0===(t&=63)?this:t<32?e.fromBits(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):e.fromBits(0,this.low<<t-32,this.unsigned)},e.prototype.shl=e.prototype.shiftLeft,e.prototype.shiftRight=function(t){return e.isLong(t)&&(t=t.toInt()),0===(t&=63)?this:t<32?e.fromBits(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):e.fromBits(this.high>>t-32,this.high>=0?0:-1,this.unsigned)},e.prototype.shr=e.prototype.shiftRight,e.prototype.shiftRightUnsigned=function(t){if(e.isLong(t)&&(t=t.toInt()),t&=63,0===t)return this;var r=this.high;if(t<32){var n=this.low;return e.fromBits(n>>>t|r<<32-t,r>>>t,this.unsigned)}return 32===t?e.fromBits(r,0,this.unsigned):e.fromBits(r>>>t-32,0,this.unsigned)},e.prototype.shru=e.prototype.shiftRightUnsigned,e.prototype.toSigned=function(){return this.unsigned?new e(this.low,this.high,!1):this},e.prototype.toUnsigned=function(){return this.unsigned?this:new e(this.low,this.high,!0)},e}))},{}],36:[function(e,t,r){(function(r){(function(r,n){"function"===typeof define&&define["amd"]?define(["ByteBuffer"],n):"function"===typeof e&&"object"===typeof t&&t&&t["exports"]?t["exports"]=n(e("bytebuffer"),!0):(r["dcodeIO"]=r["dcodeIO"]||{})["ProtoBuf"]=n(r["dcodeIO"]["ByteBuffer"])})(this,(function(t,n){"use strict";var i={};return i.ByteBuffer=t,i.Long=t.Long||null,i.VERSION="4.1.3",i.WIRE_TYPES={},i.WIRE_TYPES.VARINT=0,i.WIRE_TYPES.BITS64=1,i.WIRE_TYPES.LDELIM=2,i.WIRE_TYPES.STARTGROUP=3,i.WIRE_TYPES.ENDGROUP=4,i.WIRE_TYPES.BITS32=5,i.PACKABLE_WIRE_TYPES=[i.WIRE_TYPES.VARINT,i.WIRE_TYPES.BITS64,i.WIRE_TYPES.BITS32],i.TYPES={int32:{name:"int32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},uint32:{name:"uint32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},sint32:{name:"sint32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},int64:{name:"int64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.ZERO:void 0},uint64:{name:"uint64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.UZERO:void 0},sint64:{name:"sint64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.ZERO:void 0},bool:{name:"bool",wireType:i.WIRE_TYPES.VARINT,defaultValue:!1},double:{name:"double",wireType:i.WIRE_TYPES.BITS64,defaultValue:0},string:{name:"string",wireType:i.WIRE_TYPES.LDELIM,defaultValue:""},bytes:{name:"bytes",wireType:i.WIRE_TYPES.LDELIM,defaultValue:null},fixed32:{name:"fixed32",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},sfixed32:{name:"sfixed32",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},fixed64:{name:"fixed64",wireType:i.WIRE_TYPES.BITS64,defaultValue:i.Long?i.Long.UZERO:void 0},sfixed64:{name:"sfixed64",wireType:i.WIRE_TYPES.BITS64,defaultValue:i.Long?i.Long.ZERO:void 0},float:{name:"float",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},enum:{name:"enum",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},message:{name:"message",wireType:i.WIRE_TYPES.LDELIM,defaultValue:null},group:{name:"group",wireType:i.WIRE_TYPES.STARTGROUP,defaultValue:null}},i.MAP_KEY_TYPES=[i.TYPES["int32"],i.TYPES["sint32"],i.TYPES["sfixed32"],i.TYPES["uint32"],i.TYPES["fixed32"],i.TYPES["int64"],i.TYPES["sint64"],i.TYPES["sfixed64"],i.TYPES["uint64"],i.TYPES["fixed64"],i.TYPES["bool"],i.TYPES["string"],i.TYPES["bytes"]],i.ID_MIN=1,i.ID_MAX=536870911,i.convertFieldsToCamelCase=!1,i.populateAccessors=!0,i.populateDefaults=!0,i.Util=function(){var t={};return t.IS_NODE=!("object"!==typeof r||r+""!=="[object process]"||r["browser"]),t.XHR=function(){for(var e=[function(){return new XMLHttpRequest},function(){return new ActiveXObject("Msxml2.XMLHTTP")},function(){return new ActiveXObject("Msxml3.XMLHTTP")},function(){return new ActiveXObject("Microsoft.XMLHTTP")}],t=null,r=0;r<e.length;r++){try{t=e[r]()}catch(n){continue}break}if(!t)throw Error("XMLHttpRequest is not supported");return t},t.fetch=function(r,n){if(n&&"function"!=typeof n&&(n=null),t.IS_NODE){var i=e("fs");if(n)i.readFile(r,(function(e,t){n(e?null:""+t)}));else try{return i.readFileSync(r)}catch(s){return null}}else{var o=t.XHR();if(o.open("GET",r,!!n),o.setRequestHeader("Accept","text/plain"),"function"===typeof o.overrideMimeType&&o.overrideMimeType("text/plain"),!n)return o.send(null),200==o.status||0==o.status&&"string"===typeof o.responseText?o.responseText:null;if(o.onreadystatechange=function(){4==o.readyState&&(200==o.status||0==o.status&&"string"===typeof o.responseText?n(o.responseText):n(null))},4==o.readyState)return;o.send(null)}},t.toCamelCase=function(e){return e.replace(/_([a-zA-Z])/g,(function(e,t){return t.toUpperCase()}))},t}(),i.Lang={DELIM:/[\s\{\}=;:\[\],'"\(\)<>]/g,RULE:/^(?:required|optional|repeated|map)$/,TYPE:/^(?:double|float|int32|uint32|sint32|int64|uint64|sint64|fixed32|sfixed32|fixed64|sfixed64|bool|string|bytes)$/,NAME:/^[a-zA-Z_][a-zA-Z_0-9]*$/,TYPEDEF:/^[a-zA-Z][a-zA-Z_0-9]*$/,TYPEREF:/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)+$/,FQTYPEREF:/^(?:\.[a-zA-Z][a-zA-Z_0-9]*)+$/,NUMBER:/^-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+|([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?)|inf|nan)$/,NUMBER_DEC:/^(?:[1-9][0-9]*|0)$/,NUMBER_HEX:/^0[xX][0-9a-fA-F]+$/,NUMBER_OCT:/^0[0-7]+$/,NUMBER_FLT:/^([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?|inf|nan)$/,BOOL:/^(?:true|false)$/i,ID:/^(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,NEGID:/^\-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,WHITESPACE:/\s/,STRING:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")|(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,STRING_DQ:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,STRING_SQ:/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g},i.DotProto=function(e,t){var r={},n=function(e){this.source=e+"",this.index=0,this.line=1,this.stack=[],this._stringOpen=null},i=n.prototype;i._readString=function(){var e='"'===this._stringOpen?t.STRING_DQ:t.STRING_SQ;e.lastIndex=this.index-1;var r=e.exec(this.source);if(!r)throw Error("unterminated string");return this.index=e.lastIndex,this.stack.push(this._stringOpen),this._stringOpen=null,r[1]},i.next=function(){if(this.stack.length>0)return this.stack.shift();if(this.index>=this.source.length)return null;if(null!==this._stringOpen)return this._readString();var e,r,n;do{e=!1;while(t.WHITESPACE.test(n=this.source.charAt(this.index)))if("\n"===n&&++this.line,++this.index===this.source.length)return null;if("/"===this.source.charAt(this.index))if(++this.index,"/"===this.source.charAt(this.index)){while("\n"!==this.source.charAt(++this.index))if(this.index==this.source.length)return null;++this.index,++this.line,e=!0}else{if("*"!==(n=this.source.charAt(this.index)))return"/";do{if("\n"===n&&++this.line,++this.index===this.source.length)return null;r=n,n=this.source.charAt(this.index)}while("*"!==r||"/"!==n);++this.index,e=!0}}while(e);if(this.index===this.source.length)return null;var i=this.index;t.DELIM.lastIndex=0;var o=t.DELIM.test(this.source.charAt(i++));if(!o)while(i<this.source.length&&!t.DELIM.test(this.source.charAt(i)))++i;var s=this.source.substring(this.index,this.index=i);return'"'!==s&&"'"!==s||(this._stringOpen=s),s},i.peek=function(){if(0===this.stack.length){var e=this.next();if(null===e)return null;this.stack.push(e)}return this.stack[0]},i.skip=function(e){var t=this.next();if(t!==e)throw Error("illegal '"+t+"', '"+e+"' expected")},i.omit=function(e){return this.peek()===e&&(this.next(),!0)},i.toString=function(){return"Tokenizer ("+this.index+"/"+this.source.length+" at line "+this.line+")"},r.Tokenizer=n;var o=function(e){this.tn=new n(e),this.proto3=!1},s=o.prototype;function a(e,r){var n=-1,i=1;if("-"==e.charAt(0)&&(i=-1,e=e.substring(1)),t.NUMBER_DEC.test(e))n=parseInt(e);else if(t.NUMBER_HEX.test(e))n=parseInt(e.substring(2),16);else{if(!t.NUMBER_OCT.test(e))throw Error("illegal id value: "+(i<0?"-":"")+e);n=parseInt(e.substring(1),8)}if(n=i*n|0,!r&&n<0)throw Error("illegal id value: "+(i<0?"-":"")+e);return n}function u(e){var r=1;if("-"==e.charAt(0)&&(r=-1,e=e.substring(1)),t.NUMBER_DEC.test(e))return r*parseInt(e,10);if(t.NUMBER_HEX.test(e))return r*parseInt(e.substring(2),16);if(t.NUMBER_OCT.test(e))return r*parseInt(e.substring(1),8);if("inf"===e)return r*(1/0);if("nan"===e)return NaN;if(t.NUMBER_FLT.test(e))return r*parseFloat(e);throw Error("illegal number value: "+(r<0?"-":"")+e)}function f(e,t,r){"undefined"===typeof e[t]?e[t]=r:(Array.isArray(e[t])||(e[t]=[e[t]]),e[t].push(r))}return s.parse=function(){var e,r={name:"[ROOT]",package:null,messages:[],enums:[],imports:[],options:{},services:[]},n=!0;try{while(e=this.tn.next())switch(e){case"package":if(!n||null!==r["package"])throw Error("unexpected 'package'");if(e=this.tn.next(),!t.TYPEREF.test(e))throw Error("illegal package name: "+e);this.tn.skip(";"),r["package"]=e;break;case"import":if(!n)throw Error("unexpected 'import'");e=this.tn.peek(),"public"===e&&this.tn.next(),e=this._readString(),this.tn.skip(";"),r["imports"].push(e);break;case"syntax":if(!n)throw Error("unexpected 'syntax'");this.tn.skip("="),"proto3"===(r["syntax"]=this._readString())&&(this.proto3=!0),this.tn.skip(";");break;case"message":this._parseMessage(r,null),n=!1;break;case"enum":this._parseEnum(r),n=!1;break;case"option":this._parseOption(r);break;case"service":this._parseService(r);break;case"extend":this._parseExtend(r);break;default:throw Error("unexpected '"+e+"'")}}catch(i){throw i.message="Parse error at line "+this.tn.line+": "+i.message,i}return delete r["name"],r},o.parse=function(e){return new o(e).parse()},s._readString=function(){var e,t,r="";do{if(t=this.tn.next(),"'"!==t&&'"'!==t)throw Error("illegal string delimiter: "+t);r+=this.tn.next(),this.tn.skip(t),e=this.tn.peek()}while('"'===e||'"'===e);return r},s._readValue=function(e){var r=this.tn.peek();if('"'===r||"'"===r)return this._readString();if(this.tn.next(),t.NUMBER.test(r))return u(r);if(t.BOOL.test(r))return"true"===r.toLowerCase();if(e&&t.TYPEREF.test(r))return r;throw Error("illegal value: "+r)},s._parseOption=function(e,r){var n=this.tn.next(),i=!1;if("("===n&&(i=!0,n=this.tn.next()),!t.TYPEREF.test(n))throw Error("illegal option name: "+n);var o=n;i&&(this.tn.skip(")"),o="("+o+")",n=this.tn.peek(),t.FQTYPEREF.test(n)&&(o+=n,this.tn.next())),this.tn.skip("="),this._parseOptionValue(e,o),r||this.tn.skip(";")},s._parseOptionValue=function(e,r){var n=this.tn.peek();if("{"!==n)f(e["options"],r,this._readValue(!0));else{this.tn.skip("{");while("}"!==(n=this.tn.next())){if(!t.NAME.test(n))throw Error("illegal option name: "+r+"."+n);this.tn.omit(":")?f(e["options"],r+"."+n,this._readValue(!0)):this._parseOptionValue(e,r+"."+n)}}},s._parseService=function(e){var r=this.tn.next();if(!t.NAME.test(r))throw Error("illegal service name at line "+this.tn.line+": "+r);var n=r,i={name:n,rpc:{},options:{}};this.tn.skip("{");while("}"!==(r=this.tn.next()))if("option"===r)this._parseOption(i);else{if("rpc"!==r)throw Error("illegal service token: "+r);this._parseServiceRPC(i)}this.tn.omit(";"),e["services"].push(i)},s._parseServiceRPC=function(e){var r="rpc",n=this.tn.next();if(!t.NAME.test(n))throw Error("illegal rpc service method name: "+n);var i=n,o={request:null,response:null,request_stream:!1,response_stream:!1,options:{}};if(this.tn.skip("("),n=this.tn.next(),"stream"===n.toLowerCase()&&(o["request_stream"]=!0,n=this.tn.next()),!t.TYPEREF.test(n))throw Error("illegal rpc service request type: "+n);if(o["request"]=n,this.tn.skip(")"),n=this.tn.next(),"returns"!==n.toLowerCase())throw Error("illegal rpc service request type delimiter: "+n);if(this.tn.skip("("),n=this.tn.next(),"stream"===n.toLowerCase()&&(o["response_stream"]=!0,n=this.tn.next()),o["response"]=n,this.tn.skip(")"),n=this.tn.peek(),"{"===n){this.tn.next();while("}"!==(n=this.tn.next())){if("option"!==n)throw Error("illegal rpc service token: "+n);this._parseOption(o)}this.tn.omit(";")}else this.tn.skip(";");"undefined"===typeof e[r]&&(e[r]={}),e[r][i]=o},s._parseMessage=function(e,r){var n=!!r,i=this.tn.next(),o={name:"",fields:[],enums:[],messages:[],options:{},oneofs:{}};if(!t.NAME.test(i))throw Error("illegal "+(n?"group":"message")+" name: "+i);o["name"]=i,n&&(this.tn.skip("="),r["id"]=a(this.tn.next()),o["isGroup"]=!0),i=this.tn.peek(),"["===i&&r&&this._parseFieldOptions(r),this.tn.skip("{");while("}"!==(i=this.tn.next()))if(t.RULE.test(i))this._parseMessageField(o,i);else if("oneof"===i)this._parseMessageOneOf(o);else if("enum"===i)this._parseEnum(o);else if("message"===i)this._parseMessage(o);else if("option"===i)this._parseOption(o);else if("extensions"===i)this._parseExtensions(o);else if("extend"===i)this._parseExtend(o);else{if(!t.TYPEREF.test(i))throw Error("illegal message token: "+i);if(!this.proto3)throw Error("illegal field rule: "+i);this._parseMessageField(o,"optional",i)}return this.tn.omit(";"),e["messages"].push(o),o},s._parseMessageField=function(e,r,n){if(!t.RULE.test(r))throw Error("illegal message field rule: "+r);var i,o={rule:r,type:"",name:"",options:{},id:0};if("map"===r){if(n)throw Error("illegal type: "+n);if(this.tn.skip("<"),i=this.tn.next(),!t.TYPE.test(i)&&!t.TYPEREF.test(i))throw Error("illegal message field type: "+i);if(o["keytype"]=i,this.tn.skip(","),i=this.tn.next(),!t.TYPE.test(i)&&!t.TYPEREF.test(i))throw Error("illegal message field: "+i);if(o["type"]=i,this.tn.skip(">"),i=this.tn.next(),!t.NAME.test(i))throw Error("illegal message field name: "+i);o["name"]=i,this.tn.skip("="),o["id"]=a(this.tn.next()),i=this.tn.peek(),"["===i&&this._parseFieldOptions(o),this.tn.skip(";")}else if(n="undefined"!==typeof n?n:this.tn.next(),"group"===n){var s=this._parseMessage(e,o);if(!/^[A-Z]/.test(s["name"]))throw Error("illegal group name: "+s["name"]);o["type"]=s["name"],o["name"]=s["name"].toLowerCase(),this.tn.omit(";")}else{if(!t.TYPE.test(n)&&!t.TYPEREF.test(n))throw Error("illegal message field type: "+n);if(o["type"]=n,i=this.tn.next(),!t.NAME.test(i))throw Error("illegal message field name: "+i);o["name"]=i,this.tn.skip("="),o["id"]=a(this.tn.next()),i=this.tn.peek(),"["===i&&this._parseFieldOptions(o),this.tn.skip(";")}return e["fields"].push(o),o},s._parseMessageOneOf=function(e){var r=this.tn.next();if(!t.NAME.test(r))throw Error("illegal oneof name: "+r);var n,i=r,o=[];this.tn.skip("{");while("}"!==(r=this.tn.next()))n=this._parseMessageField(e,"optional",r),n["oneof"]=i,o.push(n["id"]);this.tn.omit(";"),e["oneofs"][i]=o},s._parseFieldOptions=function(e){this.tn.skip("[");var t=!0;while("]"!==this.tn.peek())t||this.tn.skip(","),this._parseOption(e,!0),t=!1;this.tn.next()},s._parseEnum=function(e){var r={name:"",values:[],options:{}},n=this.tn.next();if(!t.NAME.test(n))throw Error("illegal name: "+n);r["name"]=n,this.tn.skip("{");while("}"!==(n=this.tn.next()))if("option"===n)this._parseOption(r);else{if(!t.NAME.test(n))throw Error("illegal name: "+n);this.tn.skip("=");var i={name:n,id:a(this.tn.next(),!0)};n=this.tn.peek(),"["===n&&this._parseFieldOptions({options:{}}),this.tn.skip(";"),r["values"].push(i)}this.tn.omit(";"),e["enums"].push(r)},s._parseExtensions=function(t){var r=this.tn.next(),n=[];"min"===r?n.push(e.ID_MIN):"max"===r?n.push(e.ID_MAX):n.push(u(r)),this.tn.skip("to"),r=this.tn.next(),"min"===r?n.push(e.ID_MIN):"max"===r?n.push(e.ID_MAX):n.push(u(r)),this.tn.skip(";"),t["extensions"]=n},s._parseExtend=function(e){var r=this.tn.next();if(!t.TYPEREF.test(r))throw Error("illegal extend reference: "+r);var n={ref:r,fields:[]};this.tn.skip("{");while("}"!==(r=this.tn.next()))if(t.RULE.test(r))this._parseMessageField(n,r);else{if(!t.TYPEREF.test(r))throw Error("illegal extend token: "+r);if(!this.proto3)throw Error("illegal field rule: "+r);this._parseMessageField(n,"optional",r)}return this.tn.omit(";"),e["messages"].push(n),n},s.toString=function(){return"Parser at line "+this.tn.line},r.Parser=o,r}(i,i.Lang),i.Reflect=function(e){var r={},n=function(e,t,r){this.builder=e,this.parent=t,this.name=r,this.className},i=n.prototype;i.fqn=function(){var e=this.name,t=this;do{if(t=t.parent,null==t)break;e=t.name+"."+e}while(1);return e},i.toString=function(e){return(e?this.className+" ":"")+this.fqn()},i.build=function(){throw Error(this.toString(!0)+" cannot be built directly")},r.T=n;var o=function(e,t,r,i,o){n.call(this,e,t,r),this.className="Namespace",this.children=[],this.options=i||{},this.syntax=o||"proto2"},s=o.prototype=Object.create(n.prototype);s.getChildren=function(e){if(e=e||null,null==e)return this.children.slice();for(var t=[],r=0,n=this.children.length;r<n;++r)this.children[r]instanceof e&&t.push(this.children[r]);return t},s.addChild=function(e){var t;if(t=this.getChild(e.name))if(t instanceof h.Field&&t.name!==t.originalName&&null===this.getChild(t.originalName))t.name=t.originalName;else{if(!(e instanceof h.Field&&e.name!==e.originalName&&null===this.getChild(e.originalName)))throw Error("Duplicate name in namespace "+this.toString(!0)+": "+e.name);e.name=e.originalName}this.children.push(e)},s.getChild=function(e){for(var t="number"===typeof e?"id":"name",r=0,n=this.children.length;r<n;++r)if(this.children[r][t]===e)return this.children[r];return null},s.resolve=function(e,t){var n,i="string"===typeof e?e.split("."):e,o=this,s=0;if(""===i[s]){while(null!==o.parent)o=o.parent;s++}do{do{if(!(o instanceof r.Namespace)){o=null;break}if(n=o.getChild(i[s]),!n||!(n instanceof r.T)||t&&!(n instanceof r.Namespace)){o=null;break}o=n,s++}while(s<i.length);if(null!=o)break;if(null!==this.parent)return this.parent.resolve(e,t)}while(null!=o);return o},s.qn=function(e){var t=[],n=e;do{t.unshift(n.name),n=n.parent}while(null!==n);for(var i=1;i<=t.length;i++){var o=t.slice(t.length-i);if(e===this.resolve(o,e instanceof r.Namespace))return o.join(".")}return e.fqn()},s.build=function(){for(var e,t={},r=this.children,n=0,i=r.length;n<i;++n)e=r[n],e instanceof o&&(t[e.name]=e.build());return Object.defineProperty&&Object.defineProperty(t,"$options",{value:this.buildOpt()}),t},s.buildOpt=function(){for(var e={},t=Object.keys(this.options),r=0,n=t.length;r<n;++r){var i=t[r],o=this.options[t[r]];e[i]=o}return e},s.getOption=function(e){return"undefined"===typeof e?this.options:"undefined"!==typeof this.options[e]?this.options[e]:null},r.Namespace=o;var a=function(t,r,n,i){if(this.type=t,this.resolvedType=r,this.isMapKey=n,this.syntax=i,n&&e.MAP_KEY_TYPES.indexOf(t)<0)throw Error("Invalid map key type: "+t.name)},u=a.prototype;function f(r){if("string"===typeof r&&(r=e.TYPES[r]),"undefined"===typeof r.defaultValue)throw Error("default value for type "+r.name+" is not supported");return r==e.TYPES["bytes"]?new t(0):r.defaultValue}function l(t,r){if(t&&"number"===typeof t.low&&"number"===typeof t.high&&"boolean"===typeof t.unsigned&&t.low===t.low&&t.high===t.high)return new e.Long(t.low,t.high,"undefined"===typeof r?t.unsigned:r);if("string"===typeof t)return e.Long.fromString(t,r||!1,10);if("number"===typeof t)return e.Long.fromNumber(t,r||!1);throw Error("not convertible to Long")}a.defaultFieldValue=f,u.verifyValue=function(r){var n=function(e,t){throw Error("Illegal value for "+this.toString(!0)+" of type "+this.type.name+": "+e+" ("+t+")")}.bind(this);switch(this.type){case e.TYPES["int32"]:case e.TYPES["sint32"]:case e.TYPES["sfixed32"]:return("number"!==typeof r||r===r&&r%1!==0)&&n(typeof r,"not an integer"),r>4294967295?0|r:r;case e.TYPES["uint32"]:case e.TYPES["fixed32"]:return("number"!==typeof r||r===r&&r%1!==0)&&n(typeof r,"not an integer"),r<0?r>>>0:r;case e.TYPES["int64"]:case e.TYPES["sint64"]:case e.TYPES["sfixed64"]:if(e.Long)try{return l(r,!1)}catch(a){n(typeof r,a.message)}else n(typeof r,"requires Long.js");case e.TYPES["uint64"]:case e.TYPES["fixed64"]:if(e.Long)try{return l(r,!0)}catch(a){n(typeof r,a.message)}else n(typeof r,"requires Long.js");case e.TYPES["bool"]:return"boolean"!==typeof r&&n(typeof r,"not a boolean"),r;case e.TYPES["float"]:case e.TYPES["double"]:return"number"!==typeof r&&n(typeof r,"not a number"),r;case e.TYPES["string"]:return"string"===typeof r||r&&r instanceof String||n(typeof r,"not a string"),""+r;case e.TYPES["bytes"]:return t.isByteBuffer(r)?r:t.wrap(r,"base64");case e.TYPES["enum"]:var i=this.resolvedType.getChildren(e.Reflect.Enum.Value);for(s=0;s<i.length;s++){if(i[s].name==r)return i[s].id;if(i[s].id==r)return i[s].id}if("proto3"===this.syntax)return("number"!==typeof r||r===r&&r%1!==0)&&n(typeof r,"not an integer"),(r>4294967295||r<0)&&n(typeof r,"not in range for uint32"),r;n(r,"not a valid enum value");case e.TYPES["group"]:case e.TYPES["message"]:if(r&&"object"===typeof r||n(typeof r,"object expected"),r instanceof this.resolvedType.clazz)return r;if(r instanceof e.Builder.Message){var o={};for(var s in r)r.hasOwnProperty(s)&&(o[s]=r[s]);r=o}return new this.resolvedType.clazz(r)}throw Error("[INTERNAL] Illegal value for "+this.toString(!0)+": "+r+" (undefined type "+this.type+")")},u.calculateLength=function(r,n){if(null===n)return 0;var i;switch(this.type){case e.TYPES["int32"]:return n<0?t.calculateVarint64(n):t.calculateVarint32(n);case e.TYPES["uint32"]:return t.calculateVarint32(n);case e.TYPES["sint32"]:return t.calculateVarint32(t.zigZagEncode32(n));case e.TYPES["fixed32"]:case e.TYPES["sfixed32"]:case e.TYPES["float"]:return 4;case e.TYPES["int64"]:case e.TYPES["uint64"]:return t.calculateVarint64(n);case e.TYPES["sint64"]:return t.calculateVarint64(t.zigZagEncode64(n));case e.TYPES["fixed64"]:case e.TYPES["sfixed64"]:return 8;case e.TYPES["bool"]:return 1;case e.TYPES["enum"]:return t.calculateVarint32(n);case e.TYPES["double"]:return 8;case e.TYPES["string"]:return i=t.calculateUTF8Bytes(n),t.calculateVarint32(i)+i;case e.TYPES["bytes"]:if(n.remaining()<0)throw Error("Illegal value for "+this.toString(!0)+": "+n.remaining()+" bytes remaining");return t.calculateVarint32(n.remaining())+n.remaining();case e.TYPES["message"]:return i=this.resolvedType.calculate(n),t.calculateVarint32(i)+i;case e.TYPES["group"]:return i=this.resolvedType.calculate(n),i+t.calculateVarint32(r<<3|e.WIRE_TYPES.ENDGROUP)}throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+n+" (unknown type)")},u.encodeValue=function(r,n,i){if(null===n)return i;switch(this.type){case e.TYPES["int32"]:n<0?i.writeVarint64(n):i.writeVarint32(n);break;case e.TYPES["uint32"]:i.writeVarint32(n);break;case e.TYPES["sint32"]:i.writeVarint32ZigZag(n);break;case e.TYPES["fixed32"]:i.writeUint32(n);break;case e.TYPES["sfixed32"]:i.writeInt32(n);break;case e.TYPES["int64"]:case e.TYPES["uint64"]:i.writeVarint64(n);break;case e.TYPES["sint64"]:i.writeVarint64ZigZag(n);break;case e.TYPES["fixed64"]:i.writeUint64(n);break;case e.TYPES["sfixed64"]:i.writeInt64(n);break;case e.TYPES["bool"]:"string"===typeof n?i.writeVarint32("false"===n.toLowerCase()?0:!!n):i.writeVarint32(n?1:0);break;case e.TYPES["enum"]:i.writeVarint32(n);break;case e.TYPES["float"]:i.writeFloat32(n);break;case e.TYPES["double"]:i.writeFloat64(n);break;case e.TYPES["string"]:i.writeVString(n);break;case e.TYPES["bytes"]:if(n.remaining()<0)throw Error("Illegal value for "+this.toString(!0)+": "+n.remaining()+" bytes remaining");var o=n.offset;i.writeVarint32(n.remaining()),i.append(n),n.offset=o;break;case e.TYPES["message"]:var s=(new t).LE();this.resolvedType.encode(n,s),i.writeVarint32(s.offset),i.append(s.flip());break;case e.TYPES["group"]:this.resolvedType.encode(n,i),i.writeVarint32(r<<3|e.WIRE_TYPES.ENDGROUP);break;default:throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+n+" (unknown type)")}return i},u.decode=function(t,r,n){if(r!=this.type.wireType)throw Error("Unexpected wire type for element");var i,o;switch(this.type){case e.TYPES["int32"]:return 0|t.readVarint32();case e.TYPES["uint32"]:return t.readVarint32()>>>0;case e.TYPES["sint32"]:return 0|t.readVarint32ZigZag();case e.TYPES["fixed32"]:return t.readUint32()>>>0;case e.TYPES["sfixed32"]:return 0|t.readInt32();case e.TYPES["int64"]:return t.readVarint64();case e.TYPES["uint64"]:return t.readVarint64().toUnsigned();case e.TYPES["sint64"]:return t.readVarint64ZigZag();case e.TYPES["fixed64"]:return t.readUint64();case e.TYPES["sfixed64"]:return t.readInt64();case e.TYPES["bool"]:return!!t.readVarint32();case e.TYPES["enum"]:return t.readVarint32();case e.TYPES["float"]:return t.readFloat();case e.TYPES["double"]:return t.readDouble();case e.TYPES["string"]:return t.readVString();case e.TYPES["bytes"]:if(o=t.readVarint32(),t.remaining()<o)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+o+" required but got only "+t.remaining());return i=t.clone(),i.limit=i.offset+o,t.offset+=o,i;case e.TYPES["message"]:return o=t.readVarint32(),this.resolvedType.decode(t,o);case e.TYPES["group"]:return this.resolvedType.decode(t,-1,n)}throw Error("[INTERNAL] Illegal decode type")},u.valueFromString=function(r){if(!this.isMapKey)throw Error("valueFromString() called on non-map-key element");switch(this.type){case e.TYPES["int32"]:case e.TYPES["sint32"]:case e.TYPES["sfixed32"]:case e.TYPES["uint32"]:case e.TYPES["fixed32"]:return this.verifyValue(parseInt(r));case e.TYPES["int64"]:case e.TYPES["sint64"]:case e.TYPES["sfixed64"]:case e.TYPES["uint64"]:case e.TYPES["fixed64"]:return this.verifyValue(r);case e.TYPES["bool"]:return"true"===r;case e.TYPES["string"]:return this.verifyValue(r);case e.TYPES["bytes"]:return t.fromBinary(r)}},u.valueToString=function(t){if(!this.isMapKey)throw Error("valueToString() called on non-map-key element");return this.type===e.TYPES["bytes"]?t.toString("binary"):t.toString()},r.Element=a;var h=function(t,r,n,i,s,a){o.call(this,t,r,n,i,a),this.className="Message",this.extensions=[e.ID_MIN,e.ID_MAX],this.clazz=null,this.isGroup=!!s,this._fields=null,this._fieldsById=null,this._fieldsByName=null},c=h.prototype=Object.create(o.prototype);function p(t,r){var n=r.readVarint32(),i=7&n,o=n>>>3;switch(i){case e.WIRE_TYPES.VARINT:do{n=r.readUint8()}while(128===(128&n));break;case e.WIRE_TYPES.BITS64:r.offset+=8;break;case e.WIRE_TYPES.LDELIM:n=r.readVarint32(),r.offset+=n;break;case e.WIRE_TYPES.STARTGROUP:p(o,r);break;case e.WIRE_TYPES.ENDGROUP:if(o===t)return!1;throw Error("Illegal GROUPEND after unknown group: "+o+" ("+t+" expected)");case e.WIRE_TYPES.BITS32:r.offset+=4;break;default:throw Error("Illegal wire type in unknown group "+t+": "+i)}return!0}c.build=function(r){if(this.clazz&&!r)return this.clazz;var n=function(e,r){var n=r.getChildren(e.Reflect.Message.Field),i=r.getChildren(e.Reflect.Message.OneOf),o=function(s,a){e.Builder.Message.call(this);for(var u=0,f=i.length;u<f;++u)this[i[u].name]=null;for(u=0,f=n.length;u<f;++u){var l=n[u];this[l.name]=l.repeated?[]:l.map?new e.Map(l):null,!l.required&&"proto3"!==r.syntax||null===l.defaultValue||(this[l.name]=l.defaultValue)}var h;if(arguments.length>0)if(1!==arguments.length||null===s||"object"!==typeof s||!("function"!==typeof s.encode||s instanceof o)||Array.isArray(s)||s instanceof e.Map||t.isByteBuffer(s)||s instanceof ArrayBuffer||e.Long&&s instanceof e.Long)for(u=0,f=arguments.length;u<f;++u)"undefined"!==typeof(h=arguments[u])&&this.$set(n[u].name,h);else this.$set(s)},s=o.prototype=Object.create(e.Builder.Message.prototype);s.add=function(t,n,i){var o=r._fieldsByName[t];if(!i){if(!o)throw Error(this+"#"+t+" is undefined");if(!(o instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+o.toString(!0));if(!o.repeated)throw Error(this+"#"+t+" is not a repeated field");n=o.verifyValue(n,!0)}return null===this[t]&&(this[t]=[]),this[t].push(n),this},s.$add=s.add,s.set=function(t,n,i){if(t&&"object"===typeof t){for(var o in i=n,t)t.hasOwnProperty(o)&&"undefined"!==typeof(n=t[o])&&this.$set(o,n,i);return this}var s=r._fieldsByName[t];if(i)this[t]=n;else{if(!s)throw Error(this+"#"+t+" is not a field: undefined");if(!(s instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+s.toString(!0));this[s.name]=n=s.verifyValue(n)}if(s&&s.oneof){var a=this[s.oneof.name];null!==n?(null!==a&&a!==s.name&&(this[a]=null),this[s.oneof.name]=s.name):a===t&&(this[s.oneof.name]=null)}return this},s.$set=s.set,s.get=function(t,n){if(n)return this[t];var i=r._fieldsByName[t];if(!i||!(i instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: undefined");if(!(i instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+i.toString(!0));return this[i.name]},s.$get=s.get;for(var a=0;a<n.length;a++){var u=n[a];u instanceof e.Reflect.Message.ExtensionField||r.builder.options["populateAccessors"]&&function(e){var t=e.originalName.replace(/(_[a-zA-Z])/g,(function(e){return e.toUpperCase().replace("_","")}));t=t.substring(0,1).toUpperCase()+t.substring(1);var n=e.originalName.replace(/([A-Z])/g,(function(e){return"_"+e})),i=function(t,r){return this[e.name]=r?t:e.verifyValue(t),this},o=function(){return this[e.name]};null===r.getChild("set"+t)&&(s["set"+t]=i),null===r.getChild("set_"+n)&&(s["set_"+n]=i),null===r.getChild("get"+t)&&(s["get"+t]=o),null===r.getChild("get_"+n)&&(s["get_"+n]=o)}(u)}function f(r,n,i,o){if(null===r||"object"!==typeof r){if(o&&o instanceof e.Reflect.Enum){var s=e.Reflect.Enum.getName(o.object,r);if(null!==s)return s}return r}if(t.isByteBuffer(r))return n?r.toBase64():r.toBuffer();if(e.Long.isLong(r))return i?r.toString():e.Long.fromValue(r);var a;if(Array.isArray(r))return a=[],r.forEach((function(e,t){a[t]=f(e,n,i,o)})),a;if(a={},r instanceof e.Map){for(var u=r.entries(),l=u.next();!l.done;l=u.next())a[r.keyElem.valueToString(l.value[0])]=f(l.value[1],n,i,r.valueElem.resolvedType);return a}var h=r.$type,c=void 0;for(var p in r)r.hasOwnProperty(p)&&(h&&(c=h.getChild(p))?a[p]=f(r[p],n,i,c.resolvedType):a[p]=f(r[p],n,i));return a}return s.encode=function(e,n){"boolean"===typeof e&&(n=e,e=void 0);var i=!1;e||(e=new t,i=!0);var o=e.littleEndian;try{return r.encode(this,e.LE(),n),(i?e.flip():e).LE(o)}catch(s){throw e.LE(o),s}},o.encode=function(e,t,r){return new o(e).encode(t,r)},s.calculate=function(){return r.calculate(this)},s.encodeDelimited=function(e){var n=!1;e||(e=new t,n=!0);var i=(new t).LE();return r.encode(this,i).flip(),e.writeVarint32(i.remaining()),e.append(i),n?e.flip():e},s.encodeAB=function(){try{return this.encode().toArrayBuffer()}catch(e){throw e["encoded"]&&(e["encoded"]=e["encoded"].toArrayBuffer()),e}},s.toArrayBuffer=s.encodeAB,s.encodeNB=function(){try{return this.encode().toBuffer()}catch(e){throw e["encoded"]&&(e["encoded"]=e["encoded"].toBuffer()),e}},s.toBuffer=s.encodeNB,s.encode64=function(){try{return this.encode().toBase64()}catch(e){throw e["encoded"]&&(e["encoded"]=e["encoded"].toBase64()),e}},s.toBase64=s.encode64,s.encodeHex=function(){try{return this.encode().toHex()}catch(e){throw e["encoded"]&&(e["encoded"]=e["encoded"].toHex()),e}},s.toHex=s.encodeHex,s.toRaw=function(e,t){return f(this,!!e,!!t,this.$type)},s.encodeJSON=function(){return JSON.stringify(f(this,!0,!0,this.$type))},o.decode=function(e,n){"string"===typeof e&&(e=t.wrap(e,n||"base64")),e=t.isByteBuffer(e)?e:t.wrap(e);var i=e.littleEndian;try{var o=r.decode(e.LE());return e.LE(i),o}catch(s){throw e.LE(i),s}},o.decodeDelimited=function(e,n){if("string"===typeof e&&(e=t.wrap(e,n||"base64")),e=t.isByteBuffer(e)?e:t.wrap(e),e.remaining()<1)return null;var i=e.offset,o=e.readVarint32();if(e.remaining()<o)return e.offset=i,null;try{var s=r.decode(e.slice(e.offset,e.offset+o).LE());return e.offset+=o,s}catch(a){throw e.offset+=o,a}},o.decode64=function(e){return o.decode(e,"base64")},o.decodeHex=function(e){return o.decode(e,"hex")},o.decodeJSON=function(e){return new o(JSON.parse(e))},s.toString=function(){return r.toString()},Object.defineProperty&&(Object.defineProperty(o,"$options",{value:r.buildOpt()}),Object.defineProperty(s,"$options",{value:o["$options"]}),Object.defineProperty(o,"$type",{value:r}),Object.defineProperty(s,"$type",{value:r})),o}(e,this);this._fields=[],this._fieldsById={},this._fieldsByName={};for(var i,o=0,s=this.children.length;o<s;o++)if(i=this.children[o],i instanceof w||i instanceof h||i instanceof T){if(n.hasOwnProperty(i.name))throw Error("Illegal reflect child of "+this.toString(!0)+": "+i.toString(!0)+" cannot override static property '"+i.name+"'");n[i.name]=i.build()}else if(i instanceof h.Field)i.build(),this._fields.push(i),this._fieldsById[i.id]=i,this._fieldsByName[i.name]=i;else if(!(i instanceof h.OneOf)&&!(i instanceof E))throw Error("Illegal reflect child of "+this.toString(!0)+": "+this.children[o].toString(!0));return this.clazz=n},c.encode=function(e,t,r){for(var n,i,o=null,s=0,a=this._fields.length;s<a;++s)n=this._fields[s],i=e[n.name],n.required&&null===i?null===o&&(o=n):n.encode(r?i:n.verifyValue(i),t,e);if(null!==o){var u=Error("Missing at least one required field for "+this.toString(!0)+": "+o);throw u["encoded"]=t,u}return t},c.calculate=function(e){for(var t,r,n=0,i=0,o=this._fields.length;i<o;++i){if(t=this._fields[i],r=e[t.name],t.required&&null===r)throw Error("Missing at least one required field for "+this.toString(!0)+": "+t);n+=t.calculate(r,e)}return n},c.decode=function(t,r,n){r="number"===typeof r?r:-1;var i,o,s,a,u=t.offset,f=new this.clazz;while(t.offset<u+r||-1===r&&t.remaining()>0){if(i=t.readVarint32(),o=7&i,s=i>>>3,o===e.WIRE_TYPES.ENDGROUP){if(s!==n)throw Error("Illegal group end indicator for "+this.toString(!0)+": "+s+" ("+(n?n+" expected":"not a group")+")");break}if(a=this._fieldsById[s]){if(a.repeated&&!a.options["packed"])f[a.name].push(a.decode(o,t));else if(a.map){var l=a.decode(o,t);f[a.name].set(l[0],l[1])}else if(f[a.name]=a.decode(o,t),a.oneof){var h=f[a.oneof.name];null!==h&&h!==a.name&&(f[h]=null),f[a.oneof.name]=a.name}}else switch(o){case e.WIRE_TYPES.VARINT:t.readVarint32();break;case e.WIRE_TYPES.BITS32:t.offset+=4;break;case e.WIRE_TYPES.BITS64:t.offset+=8;break;case e.WIRE_TYPES.LDELIM:var c=t.readVarint32();t.offset+=c;break;case e.WIRE_TYPES.STARTGROUP:while(p(s,t));break;default:throw Error("Illegal wire type for unknown field "+s+" in "+this.toString(!0)+"#decode: "+o)}}for(var d=0,y=this._fields.length;d<y;++d)if(a=this._fields[d],null===f[a.name])if("proto3"===this.syntax)f[a.name]=a.defaultValue;else{if(a.required){var g=Error("Missing at least one required field for "+this.toString(!0)+": "+a.name);throw g["decoded"]=f,g}e.populateDefaults&&null!==a.defaultValue&&(f[a.name]=a.defaultValue)}return f},r.Message=h;var d=function(t,r,i,o,s,a,u,f,l,c){n.call(this,t,r,a),this.className="Message.Field",this.required="required"===i,this.repeated="repeated"===i,this.map="map"===i,this.keyType=o||null,this.type=s,this.resolvedType=null,this.id=u,this.options=f||{},this.defaultValue=null,this.oneof=l||null,this.syntax=c||"proto2",this.originalName=this.name,this.element=null,this.keyElement=null,!this.builder.options["convertFieldsToCamelCase"]||this instanceof h.ExtensionField||(this.name=e.Util.toCamelCase(this.name))},y=d.prototype=Object.create(n.prototype);y.build=function(){this.element=new a(this.type,this.resolvedType,!1,this.syntax),this.map&&(this.keyElement=new a(this.keyType,void 0,!0,this.syntax)),"proto3"!==this.syntax||this.repeated||this.map?"undefined"!==typeof this.options["default"]&&(this.defaultValue=this.verifyValue(this.options["default"])):this.defaultValue=a.defaultFieldValue(this.type)},y.verifyValue=function(t,r){r=r||!1;var n,i=function(e,t){throw Error("Illegal value for "+this.toString(!0)+" of type "+this.type.name+": "+e+" ("+t+")")}.bind(this);if(null===t)return this.required&&i(typeof t,"required"),"proto3"===this.syntax&&this.type!==e.TYPES["message"]&&i(typeof t,"proto3 field without field presence cannot be null"),null;if(this.repeated&&!r){Array.isArray(t)||(t=[t]);var o=[];for(n=0;n<t.length;n++)o.push(this.element.verifyValue(t[n]));return o}return this.map&&!r?t instanceof e.Map?t:(t instanceof Object||i(typeof t,"expected ProtoBuf.Map or raw object for map field"),new e.Map(this,t)):(!this.repeated&&Array.isArray(t)&&i(typeof t,"no array expected"),this.element.verifyValue(t))},y.hasWirePresence=function(t,r){if("proto3"!==this.syntax)return null!==t;if(this.oneof&&r[this.oneof.name]===this.name)return!0;switch(this.type){case e.TYPES["int32"]:case e.TYPES["sint32"]:case e.TYPES["sfixed32"]:case e.TYPES["uint32"]:case e.TYPES["fixed32"]:return 0!==t;case e.TYPES["int64"]:case e.TYPES["sint64"]:case e.TYPES["sfixed64"]:case e.TYPES["uint64"]:case e.TYPES["fixed64"]:return 0!==t.low||0!==t.high;case e.TYPES["bool"]:return t;case e.TYPES["float"]:case e.TYPES["double"]:return 0!==t;case e.TYPES["string"]:return t.length>0;case e.TYPES["bytes"]:return t.remaining()>0;case e.TYPES["enum"]:return 0!==t;case e.TYPES["message"]:return null!==t;default:return!0}},y.encode=function(r,n,i){if(null===this.type||"object"!==typeof this.type)throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0==r.length)return n;try{var o;if(this.repeated)if(this.options["packed"]&&e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0){n.writeVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),n.ensureCapacity(n.offset+=1);var s=n.offset;for(o=0;o<r.length;o++)this.element.encodeValue(this.id,r[o],n);var a=n.offset-s,u=t.calculateVarint32(a);if(u>1){var f=n.slice(s,n.offset);s+=u-1,n.offset=s,n.append(f)}n.writeVarint32(a,s-u)}else for(o=0;o<r.length;o++)n.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r[o],n);else this.map?r.forEach((function(r,i,o){var s=t.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,i)+t.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r);n.writeVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),n.writeVarint32(s),n.writeVarint32(8|this.keyType.wireType),this.keyElement.encodeValue(1,i,n),n.writeVarint32(16|this.type.wireType),this.element.encodeValue(2,r,n)}),this):this.hasWirePresence(r,i)&&(n.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r,n))}catch(l){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+l+")")}return n},y.calculate=function(r,n){if(r=this.verifyValue(r),null===this.type||"object"!==typeof this.type)throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0==r.length)return 0;var i=0;try{var o,s;if(this.repeated)if(this.options["packed"]&&e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0){for(i+=t.calculateVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),s=0,o=0;o<r.length;o++)s+=this.element.calculateLength(this.id,r[o]);i+=t.calculateVarint32(s),i+=s}else for(o=0;o<r.length;o++)i+=t.calculateVarint32(this.id<<3|this.type.wireType),i+=this.element.calculateLength(this.id,r[o]);else this.map?r.forEach((function(r,n,o){var s=t.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,n)+t.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r);i+=t.calculateVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),i+=t.calculateVarint32(s),i+=s}),this):this.hasWirePresence(r,n)&&(i+=t.calculateVarint32(this.id<<3|this.type.wireType),i+=this.element.calculateLength(this.id,r))}catch(a){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+a+")")}return i},y.decode=function(t,r,n){var i,o,s=!this.map&&t==this.type.wireType||!n&&this.repeated&&this.options["packed"]&&t==e.WIRE_TYPES.LDELIM||this.map&&t==e.WIRE_TYPES.LDELIM;if(!s)throw Error("Illegal wire type for field "+this.toString(!0)+": "+t+" ("+this.type.wireType+" expected)");if(t==e.WIRE_TYPES.LDELIM&&this.repeated&&this.options["packed"]&&e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0&&!n){o=r.readVarint32(),o=r.offset+o;var u=[];while(r.offset<o)u.push(this.decode(this.type.wireType,r,!0));return u}if(this.map){var f=a.defaultFieldValue(this.keyType);if(i=a.defaultFieldValue(this.type),o=r.readVarint32(),r.remaining()<o)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+o+" required but got only "+r.remaining());var l=r.clone();l.limit=l.offset+o,r.offset+=o;while(l.remaining()>0){var h=l.readVarint32();t=7&h;var c=h>>>3;if(1===c)f=this.keyElement.decode(l,t,c);else{if(2!==c)throw Error("Unexpected tag in map field key/value submessage");i=this.element.decode(l,t,c)}}return[f,i]}return this.element.decode(r,t,this.id)},r.Message.Field=d;var g=function(e,t,r,n,i,o,s){d.call(this,e,t,r,null,n,i,o,s),this.extension};g.prototype=Object.create(d.prototype),r.Message.ExtensionField=g;var m=function(e,t,r){n.call(this,e,t,r),this.fields=[]};r.Message.OneOf=m;var w=function(e,t,r,n,i){o.call(this,e,t,r,n,i),this.className="Enum",this.object=null};w.getName=function(e,t){for(var r,n=Object.keys(e),i=0;i<n.length;++i)if(e[r=n[i]]===t)return r;return null};var b=w.prototype=Object.create(o.prototype);b.build=function(t){if(this.object&&!t)return this.object;for(var r=new e.Builder.Enum,n=this.getChildren(w.Value),i=0,o=n.length;i<o;++i)r[n[i]["name"]]=n[i]["id"];return Object.defineProperty&&Object.defineProperty(r,"$options",{value:this.buildOpt(),enumerable:!1}),this.object=r},r.Enum=w;var v=function(e,t,r,i){n.call(this,e,t,r),this.className="Enum.Value",this.id=i};v.prototype=Object.create(n.prototype),r.Enum.Value=v;var E=function(e,t,r,i){n.call(this,e,t,r),this.field=i};E.prototype=Object.create(n.prototype),r.Extension=E;var T=function(e,t,r,n){o.call(this,e,t,r,n),this.className="Service",this.clazz=null},_=T.prototype=Object.create(o.prototype);_.build=function(r){return this.clazz&&!r?this.clazz:this.clazz=function(e,r){for(var n=function(t){e.Builder.Service.call(this),this.rpcImpl=t||function(e,t,r){setTimeout(r.bind(this,Error("Not implemented, see: https://github.com/dcodeIO/ProtoBuf.js/wiki/Services")),0)}},i=n.prototype=Object.create(e.Builder.Service.prototype),o=r.getChildren(e.Reflect.Service.RPCMethod),s=0;s<o.length;s++)(function(e){i[e.name]=function(n,i){try{try{n=e.resolvedRequestType.clazz.decode(t.wrap(n))}catch(o){if(!(o instanceof TypeError))throw o}if(null===n||"object"!==typeof n)throw Error("Illegal arguments");n instanceof e.resolvedRequestType.clazz||(n=new e.resolvedRequestType.clazz(n)),this.rpcImpl(e.fqn(),n,(function(t,n){if(t)i(t);else{try{n=e.resolvedResponseType.clazz.decode(n)}catch(o){}n&&n instanceof e.resolvedResponseType.clazz?i(null,n):i(Error("Illegal response type received in service method "+r.name+"#"+e.name))}}))}catch(o){setTimeout(i.bind(this,o),0)}},n[e.name]=function(t,r,i){new n(t)[e.name](r,i)},Object.defineProperty&&(Object.defineProperty(n[e.name],"$options",{value:e.buildOpt()}),Object.defineProperty(i[e.name],"$options",{value:n[e.name]["$options"]}))})(o[s]);return Object.defineProperty&&(Object.defineProperty(n,"$options",{value:r.buildOpt()}),Object.defineProperty(i,"$options",{value:n["$options"]}),Object.defineProperty(n,"$type",{value:r}),Object.defineProperty(i,"$type",{value:r})),n}(e,this)},r.Service=T;var S=function(e,t,r,i){n.call(this,e,t,r),this.className="Service.Method",this.options=i||{}},R=S.prototype=Object.create(n.prototype);R.buildOpt=s.buildOpt,r.Service.Method=S;var C=function(e,t,r,n,i,o,s,a){S.call(this,e,t,r,a),this.className="Service.RPCMethod",this.requestName=n,this.responseName=i,this.requestStream=o,this.responseStream=s,this.resolvedRequestType=null,this.resolvedResponseType=null};return C.prototype=Object.create(S.prototype),r.Service.RPCMethod=C,r}(i),i.Builder=function(t,r,n){var i=function(e){this.ns=new n.Namespace(this,null,""),this.ptr=this.ns,this.resolved=!1,this.result=null,this.files={},this.importRoot=null,this.options=e||{}},o=i.prototype;function s(e){e["messages"]&&e["messages"].forEach((function(t){t["syntax"]=e["syntax"],s(t)})),e["enums"]&&e["enums"].forEach((function(t){t["syntax"]=e["syntax"]}))}return i.isMessage=function(e){return"string"===typeof e["name"]&&("undefined"===typeof e["values"]&&"undefined"===typeof e["rpc"])},i.isMessageField=function(e){return"string"===typeof e["rule"]&&"string"===typeof e["name"]&&"string"===typeof e["type"]&&"undefined"!==typeof e["id"]},i.isEnum=function(e){return"string"===typeof e["name"]&&!("undefined"===typeof e["values"]||!Array.isArray(e["values"])||0===e["values"].length)},i.isService=function(e){return!("string"!==typeof e["name"]||"object"!==typeof e["rpc"]||!e["rpc"])},i.isExtend=function(e){return"string"===typeof e["ref"]},o.reset=function(){return this.ptr=this.ns,this},o.define=function(e){if("string"!==typeof e||!r.TYPEREF.test(e))throw Error("illegal namespace: "+e);return e.split(".").forEach((function(e){var t=this.ptr.getChild(e);null===t&&this.ptr.addChild(t=new n.Namespace(this,this.ptr,e)),this.ptr=t}),this),this},o.create=function(e){if(!e)return this;if(Array.isArray(e)){if(0===e.length)return this;e=e.slice()}else e=[e];var r=[e];while(r.length>0){if(e=r.pop(),!Array.isArray(e))throw Error("not a valid namespace: "+JSON.stringify(e));while(e.length>0){var o=e.shift();if(i.isMessage(o)){var s=new n.Message(this,this.ptr,o["name"],o["options"],o["isGroup"],o["syntax"]),a={};o["oneofs"]&&Object.keys(o["oneofs"]).forEach((function(e){s.addChild(a[e]=new n.Message.OneOf(this,s,e))}),this),o["fields"]&&o["fields"].forEach((function(e){if(null!==s.getChild(0|e["id"]))throw Error("duplicate or invalid field id in "+s.name+": "+e["id"]);if(e["options"]&&"object"!==typeof e["options"])throw Error("illegal field options in "+s.name+"#"+e["name"]);var t=null;if("string"===typeof e["oneof"]&&!(t=a[e["oneof"]]))throw Error("illegal oneof in "+s.name+"#"+e["name"]+": "+e["oneof"]);e=new n.Message.Field(this,s,e["rule"],e["keytype"],e["type"],e["name"],e["id"],e["options"],t,o["syntax"]),t&&t.fields.push(e),s.addChild(e)}),this);var u=[];if(o["enums"]&&o["enums"].forEach((function(e){u.push(e)})),o["messages"]&&o["messages"].forEach((function(e){u.push(e)})),o["services"]&&o["services"].forEach((function(e){u.push(e)})),o["extensions"]&&(s.extensions=o["extensions"],s.extensions[0]<t.ID_MIN&&(s.extensions[0]=t.ID_MIN),s.extensions[1]>t.ID_MAX&&(s.extensions[1]=t.ID_MAX)),this.ptr.addChild(s),u.length>0){r.push(e),e=u,u=null,this.ptr=s,s=null;continue}u=null}else if(i.isEnum(o))s=new n.Enum(this,this.ptr,o["name"],o["options"],o["syntax"]),o["values"].forEach((function(e){s.addChild(new n.Enum.Value(this,s,e["name"],e["id"]))}),this),this.ptr.addChild(s);else if(i.isService(o))s=new n.Service(this,this.ptr,o["name"],o["options"]),Object.keys(o["rpc"]).forEach((function(e){var t=o["rpc"][e];s.addChild(new n.Service.RPCMethod(this,s,e,t["request"],t["response"],!!t["request_stream"],!!t["response_stream"],t["options"]))}),this),this.ptr.addChild(s);else{if(!i.isExtend(o))throw Error("not a valid definition: "+JSON.stringify(o));if(s=this.ptr.resolve(o["ref"],!0),s)o["fields"].forEach((function(e){if(null!==s.getChild(0|e["id"]))throw Error("duplicate extended field id in "+s.name+": "+e["id"]);if(e["id"]<s.extensions[0]||e["id"]>s.extensions[1])throw Error("illegal extended field id in "+s.name+": "+e["id"]+" ("+s.extensions.join(" to ")+" expected)");var r=e["name"];this.options["convertFieldsToCamelCase"]&&(r=t.Util.toCamelCase(r));var i=new n.Message.ExtensionField(this,s,e["rule"],e["type"],this.ptr.fqn()+"."+r,e["id"],e["options"]),o=new n.Extension(this,this.ptr,e["name"],i);i.extension=o,this.ptr.addChild(o),s.addChild(i)}),this);else if(!/\.?google\.protobuf\./.test(o["ref"]))throw Error("extended message "+o["ref"]+" is not defined")}o=null,s=null}e=null,this.ptr=this.ptr.parent}return this.resolved=!1,this.result=null,this},o["import"]=function(r,n){var i="/";if("string"===typeof n){if(t.Util.IS_NODE&&(n=e("path")["resolve"](n)),!0===this.files[n])return this.reset();this.files[n]=!0}else if("object"===typeof n){var o=n.root;t.Util.IS_NODE&&(o=e("path")["resolve"](o)),(o.indexOf("\\")>=0||n.file.indexOf("\\")>=0)&&(i="\\");var a=o+i+n.file;if(!0===this.files[a])return this.reset();this.files[a]=!0}if(r["imports"]&&r["imports"].length>0){var u,f=!1;"object"===typeof n?(this.importRoot=n["root"],f=!0,u=this.importRoot,n=n["file"],(u.indexOf("\\")>=0||n.indexOf("\\")>=0)&&(i="\\")):"string"===typeof n?this.importRoot?u=this.importRoot:n.indexOf("/")>=0?(u=n.replace(/\/[^\/]*$/,""),""===u&&(u="/")):n.indexOf("\\")>=0?(u=n.replace(/\\[^\\]*$/,""),i="\\"):u=".":u=null;for(var l=0;l<r["imports"].length;l++)if("string"===typeof r["imports"][l]){if(!u)throw Error("cannot determine import root");var h=r["imports"][l];if("google/protobuf/descriptor.proto"===h)continue;if(h=u+i+h,!0===this.files[h])continue;/\.proto$/i.test(h)&&!t.DotProto&&(h=h.replace(/\.proto$/,".json"));var c=t.Util.fetch(h);if(null===c)throw Error("failed to import '"+h+"' in '"+n+"': file not found");/\.json$/i.test(h)?this["import"](JSON.parse(c+""),h):this["import"](t.DotProto.Parser.parse(c),h)}else n?/\.(\w+)$/.test(n)?this["import"](r["imports"][l],n.replace(/^(.+)\.(\w+)$/,(function(e,t,r){return t+"_import"+l+"."+r}))):this["import"](r["imports"][l],n+"_import"+l):this["import"](r["imports"][l]);f&&(this.importRoot=null)}r["package"]&&this.define(r["package"]),r["syntax"]&&s(r);var p=this.ptr;return r["options"]&&Object.keys(r["options"]).forEach((function(e){p.options[e]=r["options"][e]})),r["messages"]&&(this.create(r["messages"]),this.ptr=p),r["enums"]&&(this.create(r["enums"]),this.ptr=p),r["services"]&&(this.create(r["services"]),this.ptr=p),r["extends"]&&this.create(r["extends"]),this.reset()},o.resolveAll=function(){var e;if(null==this.ptr||"object"===typeof this.ptr.type)return this;if(this.ptr instanceof n.Namespace)this.ptr.children.forEach((function(e){this.ptr=e,this.resolveAll()}),this);else if(this.ptr instanceof n.Message.Field){if(r.TYPE.test(this.ptr.type))this.ptr.type=t.TYPES[this.ptr.type];else{if(!r.TYPEREF.test(this.ptr.type))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(e=(this.ptr instanceof n.Message.ExtensionField?this.ptr.extension.parent:this.ptr.parent).resolve(this.ptr.type,!0),!e)throw Error("unresolvable type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(this.ptr.resolvedType=e,e instanceof n.Enum){if(this.ptr.type=t.TYPES["enum"],"proto3"===this.ptr.syntax&&"proto3"!==e.syntax)throw Error("proto3 message cannot reference proto2 enum")}else{if(!(e instanceof n.Message))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);this.ptr.type=e.isGroup?t.TYPES["group"]:t.TYPES["message"]}}if(this.ptr.map){if(!r.TYPE.test(this.ptr.keyType))throw Error("illegal key type for map field in "+this.ptr.toString(!0)+": "+this.ptr.keyType);this.ptr.keyType=t.TYPES[this.ptr.keyType]}}else if(this.ptr instanceof t.Reflect.Service.Method){if(!(this.ptr instanceof t.Reflect.Service.RPCMethod))throw Error("illegal service type in "+this.ptr.toString(!0));if(e=this.ptr.parent.resolve(this.ptr.requestName,!0),!e||!(e instanceof t.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.requestName);if(this.ptr.resolvedRequestType=e,e=this.ptr.parent.resolve(this.ptr.responseName,!0),!e||!(e instanceof t.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.responseName);this.ptr.resolvedResponseType=e}else if(!(this.ptr instanceof t.Reflect.Message.OneOf)&&!(this.ptr instanceof t.Reflect.Extension)&&!(this.ptr instanceof t.Reflect.Enum.Value))throw Error("illegal object in namespace: "+typeof this.ptr+": "+this.ptr);return this.reset()},o.build=function(e){if(this.reset(),this.resolved||(this.resolveAll(),this.resolved=!0,this.result=null),null===this.result&&(this.result=this.ns.build()),!e)return this.result;for(var t="string"===typeof e?e.split("."):e,r=this.result,n=0;n<t.length;n++){if(!r[t[n]]){r=null;break}r=r[t[n]]}return r},o.lookup=function(e,t){return e?this.ns.resolve(e,t):this.ns},o.toString=function(){return"Builder"},i.Message=function(){},i.Enum=function(){},i.Service=function(){},i}(i,i.Lang,i.Reflect),i.Map=function(e,t){var r=function(e,r){if(!e.map)throw Error("field is not a map");if(this.field=e,this.keyElem=new t.Element(e.keyType,null,!0,e.syntax),this.valueElem=new t.Element(e.type,e.resolvedType,!1,e.syntax),this.map={},Object.defineProperty(this,"size",{get:function(){return Object.keys(this.map).length}}),r)for(var n=Object.keys(r),i=0;i<n.length;i++){var o=this.keyElem.valueFromString(n[i]),s=this.valueElem.verifyValue(r[n[i]]);this.map[this.keyElem.valueToString(o)]={key:o,value:s}}},n=r.prototype;function i(e){var t=0;return{next:function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}}return n.clear=function(){this.map={}},n["delete"]=function(e){var t=this.keyElem.valueToString(this.keyElem.verifyValue(e)),r=t in this.map;return delete this.map[t],r},n.entries=function(){for(var e,t=[],r=Object.keys(this.map),n=0;n<r.length;n++)t.push([(e=this.map[r[n]]).key,e.value]);return i(t)},n.keys=function(){for(var e=[],t=Object.keys(this.map),r=0;r<t.length;r++)e.push(this.map[t[r]].key);return i(e)},n.values=function(){for(var e=[],t=Object.keys(this.map),r=0;r<t.length;r++)e.push(this.map[t[r]].value);return i(e)},n.forEach=function(e,t){for(var r,n=Object.keys(this.map),i=0;i<n.length;i++)e.call(t,(r=this.map[n[i]]).value,r.key,this)},n.set=function(e,t){var r=this.keyElem.verifyValue(e),n=this.valueElem.verifyValue(t);return this.map[this.keyElem.valueToString(r)]={key:r,value:n},this},n.get=function(e){var t=this.keyElem.valueToString(this.keyElem.verifyValue(e));if(t in this.map)return this.map[t].value},n.has=function(e){var t=this.keyElem.valueToString(this.keyElem.verifyValue(e));return t in this.map},r}(0,i.Reflect),i.loadProto=function(e,t,r){return("string"===typeof t||t&&"string"===typeof t["file"]&&"string"===typeof t["root"])&&(r=t,t=void 0),i.loadJson(i.DotProto.Parser.parse(e),t,r)},i.protoFromString=i.loadProto,i.loadProtoFile=function(e,t,r){if(t&&"object"===typeof t?(r=t,t=null):t&&"function"===typeof t||(t=null),t)return i.Util.fetch("string"===typeof e?e:e["root"]+"/"+e["file"],(function(n){if(null!==n)try{t(null,i.loadProto(n,r,e))}catch(o){t(o)}else t(Error("Failed to fetch file"))}));var n=i.Util.fetch("object"===typeof e?e["root"]+"/"+e["file"]:e);return null===n?null:i.loadProto(n,r,e)},i.protoFromFile=i.loadProtoFile,i.newBuilder=function(e){return e=e||{},"undefined"===typeof e["convertFieldsToCamelCase"]&&(e["convertFieldsToCamelCase"]=i.convertFieldsToCamelCase),"undefined"===typeof e["populateAccessors"]&&(e["populateAccessors"]=i.populateAccessors),new i.Builder(e)},i.loadJson=function(e,t,r){return("string"===typeof t||t&&"string"===typeof t["file"]&&"string"===typeof t["root"])&&(r=t,t=null),t&&"object"===typeof t||(t=i.newBuilder()),"string"===typeof e&&(e=JSON.parse(e)),t["import"](e,r),t.resolveAll(),t},i.loadJsonFile=function(e,t,r){if(t&&"object"===typeof t?(r=t,t=null):t&&"function"===typeof t||(t=null),t)return i.Util.fetch("string"===typeof e?e:e["root"]+"/"+e["file"],(function(n){if(null!==n)try{t(null,i.loadJson(JSON.parse(n),r,e))}catch(o){t(o)}else t(Error("Failed to fetch file"))}));var n=i.Util.fetch("object"===typeof e?e["root"]+"/"+e["file"]:e);return null===n?null:i.loadJson(JSON.parse(n),r,e)},i}))}).call(this,e("_process"))},{_process:50,bytebuffer:31,fs:39,path:39}],37:[function(e,t,r){},{}],38:[function(e,t,r){arguments[4][29][0].apply(r,arguments)},{dup:29}],39:[function(e,t,r){arguments[4][37][0].apply(r,arguments)},{dup:37}],40:[function(e,t,r){"use strict";var n=e("base64-js"),i=e("ieee754");r.Buffer=u,r.SlowBuffer=w,r.INSPECT_MAX_BYTES=50;var o=2147483647;function s(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(t){return!1}}function a(e){if(e>o)throw new RangeError("Invalid typed array length");var t=new Uint8Array(e);return t.__proto__=u.prototype,t}function u(e,t,r){if("number"===typeof e){if("string"===typeof t)throw new Error("If encoding is specified then the first argument must be a string");return c(e)}return f(e,t,r)}function f(e,t,r){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return $(e)?y(e,t,r):"string"===typeof e?p(e,t):g(e)}function l(e){if("number"!==typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,t,r){return l(e),e<=0?a(e):void 0!==t?"string"===typeof r?a(e).fill(t,r):a(e).fill(t):a(e)}function c(e){return l(e),a(e<0?0:0|m(e))}function p(e,t){if("string"===typeof t&&""!==t||(t="utf8"),!u.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var r=0|b(e,t),n=a(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}function d(e){for(var t=e.length<0?0:0|m(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function y(e,t,r){if(t<0||e.byteLength<t)throw new RangeError("'offset' is out of bounds");if(e.byteLength<t+(r||0))throw new RangeError("'length' is out of bounds");var n;return n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),n.__proto__=u.prototype,n}function g(e){if(u.isBuffer(e)){var t=0|m(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}if(e){if(Q(e)||"length"in e)return"number"!==typeof e.length||J(e.length)?a(0):d(e);if("Buffer"===e.type&&Array.isArray(e.data))return d(e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(e){if(e>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|e}function w(e){return+e!=e&&(e=0),u.alloc(+e)}function b(e,t){if(u.isBuffer(e))return e.length;if(Q(e)||$(e))return e.byteLength;"string"!==typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return z(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Z(e).length;default:if(n)return z(e).length;t=(""+t).toLowerCase(),n=!0}}function v(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if(r>>>=0,t>>>=0,r<=t)return"";e||(e="utf8");while(1)switch(e){case"hex":return k(this,t,r);case"utf8":case"utf-8":return x(this,t,r);case"ascii":return O(this,t,r);case"latin1":case"binary":return U(this,t,r);case"base64":return B(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function E(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function T(e,t,r,n,i){if(0===e.length)return-1;if("string"===typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,J(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"===typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:_(e,t,r,n,i);if("number"===typeof t)return t&=255,"function"===typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):_(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function _(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&(n=String(n).toLowerCase(),"ucs2"===n||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function f(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var l=-1;for(o=r;o<a;o++)if(f(e,o)===f(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var h=!0,c=0;c<u;c++)if(f(e,o+c)!==f(t,c)){h=!1;break}if(h)return o}return-1}function S(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n),n>i&&(n=i)):n=i;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(J(a))return s;e[r+s]=a}return s}function R(e,t,r,n){return X(z(t,e.length-r),e,r,n)}function C(e,t,r,n){return X(W(t),e,r,n)}function L(e,t,r,n){return C(e,t,r,n)}function I(e,t,r,n){return X(Z(t),e,r,n)}function A(e,t,r,n){return X(K(t,e.length-r),e,r,n)}function B(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function x(e,t,r){r=Math.min(e.length,r);var n=[],i=t;while(i<r){var o,s,a,u,f=e[i],l=null,h=f>239?4:f>223?3:f>191?2:1;if(i+h<=r)switch(h){case 1:f<128&&(l=f);break;case 2:o=e[i+1],128===(192&o)&&(u=(31&f)<<6|63&o,u>127&&(l=u));break;case 3:o=e[i+1],s=e[i+2],128===(192&o)&&128===(192&s)&&(u=(15&f)<<12|(63&o)<<6|63&s,u>2047&&(u<55296||u>57343)&&(l=u));break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128===(192&o)&&128===(192&s)&&128===(192&a)&&(u=(15&f)<<18|(63&o)<<12|(63&s)<<6|63&a,u>65535&&u<1114112&&(l=u))}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}return P(n)}r.kMaxLength=o,u.TYPED_ARRAY_SUPPORT=s(),u.TYPED_ARRAY_SUPPORT||"undefined"===typeof console||"function"!==typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),"undefined"!==typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),u.poolSize=8192,u.from=function(e,t,r){return f(e,t,r)},u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,u.alloc=function(e,t,r){return h(e,t,r)},u.allocUnsafe=function(e){return c(e)},u.allocUnsafeSlow=function(e){return c(e)},u.isBuffer=function(e){return null!=e&&!0===e._isBuffer},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!u.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},u.byteLength=b,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)E(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)E(this,t,t+3),E(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)E(this,t,t+7),E(this,t+1,t+6),E(this,t+2,t+5),E(this,t+3,t+4);return this},u.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?x(this,0,e):v.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,s=r-t,a=Math.min(o,s),f=this.slice(n,i),l=e.slice(t,r),h=0;h<a;++h)if(f[h]!==l[h]){o=f[h],s=l[h];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return T(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return T(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"===typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return S(this,e,t,r);case"utf8":case"utf-8":return R(this,e,t,r);case"ascii":return C(this,e,t,r);case"latin1":case"binary":return L(this,e,t,r);case"base64":return I(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var N=4096;function P(e){var t=e.length;if(t<=N)return String.fromCharCode.apply(String,e);var r="",n=0;while(n<t)r+=String.fromCharCode.apply(String,e.slice(n,n+=N));return r}function O(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function U(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function k(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=G(e[o]);return i}function M(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function D(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function V(e,t,r,n,i,o){if(!u.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function q(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function F(e,t,r,n,o){return t=+t,r>>>=0,o||q(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function j(e,t,r,n,o){return t=+t,r>>>=0,o||q(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r,e<0&&(e=0)):e>r&&(e=r),t<0?(t+=r,t<0&&(t=0)):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return n.__proto__=u.prototype,n},u.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);var n=this[e],i=1,o=0;while(++o<t&&(i*=256))n+=this[e+o]*i;return n},u.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);var n=this[e+--t],i=1;while(t>0&&(i*=256))n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return e>>>=0,t||D(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return e>>>=0,t||D(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return e>>>=0,t||D(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return e>>>=0,t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return e>>>=0,t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);var n=this[e],i=1,o=0;while(++o<t&&(i*=256))n+=this[e+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);var n=t,i=1,o=this[e+--n];while(n>0&&(i*=256))o+=this[e+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},u.prototype.readInt8=function(e,t){return e>>>=0,t||D(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){e>>>=0,t||D(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){e>>>=0,t||D(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return e>>>=0,t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return e>>>=0,t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return e>>>=0,t||D(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return e>>>=0,t||D(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return e>>>=0,t||D(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return e>>>=0,t||D(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;V(this,e,t,r,i,0)}var o=1,s=0;this[t]=255&e;while(++s<r&&(o*=256))this[t+s]=e/o&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;V(this,e,t,r,i,0)}var o=r-1,s=1;this[t+o]=255&e;while(--o>=0&&(s*=256))this[t+o]=e/s&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,1,255,0),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);V(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;this[t]=255&e;while(++o<r&&(s*=256))e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);V(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;this[t+o]=255&e;while(--o>=0&&(s*=256))e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||V(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeFloatLE=function(e,t,r){return F(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return F(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return j(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return j(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},u.prototype.fill=function(e,t,r,n){if("string"===typeof e){if("string"===typeof t?(n=t,t=0,r=this.length):"string"===typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!==typeof n)throw new TypeError("encoding must be a string");if("string"===typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"===typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"===typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=u.isBuffer(e)?e:new u(e,n),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var Y=/[^+/0-9A-Za-z-_]/g;function H(e){if(e=e.trim().replace(Y,""),e.length<2)return"";while(e.length%4!==0)e+="=";return e}function G(e){return e<16?"0"+e.toString(16):e.toString(16)}function z(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function K(e,t){for(var r,n,i,o=[],s=0;s<e.length;++s){if((t-=2)<0)break;r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n)}return o}function Z(e){return n.toByteArray(H(e))}function X(e,t,r,n){for(var i=0;i<n;++i){if(i+r>=t.length||i>=e.length)break;t[i+r]=e[i]}return i}function $(e){return e instanceof ArrayBuffer||null!=e&&null!=e.constructor&&"ArrayBuffer"===e.constructor.name&&"number"===typeof e.byteLength}function Q(e){return"function"===typeof ArrayBuffer.isView&&ArrayBuffer.isView(e)}function J(e){return e!==e}},{"base64-js":38,ieee754:45}],41:[function(e,t,r){t.exports={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},{}],42:[function(e,t,r){(function(e){function t(e){return Array.isArray?Array.isArray(e):"[object Array]"===g(e)}function n(e){return"boolean"===typeof e}function i(e){return null===e}function o(e){return null==e}function s(e){return"number"===typeof e}function a(e){return"string"===typeof e}function u(e){return"symbol"===typeof e}function f(e){return void 0===e}function l(e){return"[object RegExp]"===g(e)}function h(e){return"object"===typeof e&&null!==e}function c(e){return"[object Date]"===g(e)}function p(e){return"[object Error]"===g(e)||e instanceof Error}function d(e){return"function"===typeof e}function y(e){return null===e||"boolean"===typeof e||"number"===typeof e||"string"===typeof e||"symbol"===typeof e||"undefined"===typeof e}function g(e){return Object.prototype.toString.call(e)}r.isArray=t,r.isBoolean=n,r.isNull=i,r.isNullOrUndefined=o,r.isNumber=s,r.isString=a,r.isSymbol=u,r.isUndefined=f,r.isRegExp=l,r.isObject=h,r.isDate=c,r.isError=p,r.isFunction=d,r.isPrimitive=y,r.isBuffer=e.isBuffer}).call(this,{isBuffer:e("../../is-buffer/index.js")})},{"../../is-buffer/index.js":47}],43:[function(e,t,r){function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(e){return"function"===typeof e}function o(e){return"number"===typeof e}function s(e){return"object"===typeof e&&null!==e}function a(e){return void 0===e}t.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(e){if(!o(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},n.prototype.emit=function(e){var t,r,n,o,u,f;if(this._events||(this._events={}),"error"===e&&(!this._events.error||s(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var l=new Error('Uncaught, unspecified "error" event. ('+t+")");throw l.context=t,l}if(r=this._events[e],a(r))return!1;if(i(r))switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:o=Array.prototype.slice.call(arguments,1),r.apply(this,o)}else if(s(r))for(o=Array.prototype.slice.call(arguments,1),f=r.slice(),n=f.length,u=0;u<n;u++)f[u].apply(this,o);return!0},n.prototype.addListener=function(e,t){var r;if(!i(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,i(t.listener)?t.listener:t),this._events[e]?s(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,s(this._events[e])&&!this._events[e].warned&&(r=a(this._maxListeners)?n.defaultMaxListeners:this._maxListeners,r&&r>0&&this._events[e].length>r&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"===typeof console.trace&&console.trace())),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(e,t){if(!i(t))throw TypeError("listener must be a function");var r=!1;function n(){this.removeListener(e,n),r||(r=!0,t.apply(this,arguments))}return n.listener=t,this.on(e,n),this},n.prototype.removeListener=function(e,t){var r,n,o,a;if(!i(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(r=this._events[e],o=r.length,n=-1,r===t||i(r.listener)&&r.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(s(r)){for(a=o;a-- >0;)if(r[a]===t||r[a].listener&&r[a].listener===t){n=a;break}if(n<0)return this;1===r.length?(r.length=0,delete this._events[e]):r.splice(n,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},n.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r=this._events[e],i(r))this.removeListener(e,r);else if(r)while(r.length)this.removeListener(e,r[r.length-1]);return delete this._events[e],this},n.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?i(this._events[e])?[this._events[e]]:this._events[e].slice():[],t},n.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(i(t))return 1;if(t)return t.length}return 0},n.listenerCount=function(e,t){return e.listenerCount(t)}},{}],44:[function(e,t,r){var n=e("http"),i=e("url"),o=t.exports;for(var s in n)n.hasOwnProperty(s)&&(o[s]=n[s]);function a(e){if("string"===typeof e&&(e=i.parse(e)),e.protocol||(e.protocol="https:"),"https:"!==e.protocol)throw new Error('Protocol "'+e.protocol+'" not supported. Expected "https:"');return e}o.request=function(e,t){return e=a(e),n.request.call(this,e,t)},o.get=function(e,t){return e=a(e),n.get.call(this,e,t)}},{http:70,url:76}],45:[function(e,t,r){arguments[4][32][0].apply(r,arguments)},{dup:32}],46:[function(e,t,r){"function"===typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},{}],47:[function(e,t,r){function n(e){return!!e.constructor&&"function"===typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function i(e){return"function"===typeof e.readFloatLE&&"function"===typeof e.slice&&n(e.slice(0,0))}t.exports=function(e){return null!=e&&(n(e)||i(e)||!!e._isBuffer)}},{}],48:[function(e,t,r){arguments[4][34][0].apply(r,arguments)},{dup:34}],49:[function(e,t,r){(function(e){"use strict";function r(t,r,n,i){if("function"!==typeof t)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,r)}));case 3:return e.nextTick((function(){t.call(null,r,n)}));case 4:return e.nextTick((function(){t.call(null,r,n,i)}));default:o=new Array(a-1),s=0;while(s<o.length)o[s++]=arguments[s];return e.nextTick((function(){t.apply(null,o)}))}}!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports=r:t.exports=e.nextTick}).call(this,e("_process"))},{_process:50}],50:[function(e,t,r){var n,i,o=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function u(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}function f(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{return i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(function(){try{n="function"===typeof setTimeout?setTimeout:s}catch(e){n=s}try{i="function"===typeof clearTimeout?clearTimeout:a}catch(e){i=a}})();var l,h=[],c=!1,p=-1;function d(){c&&l&&(c=!1,l.length?h=l.concat(h):p=-1,h.length&&y())}function y(){if(!c){var e=u(d);c=!0;var t=h.length;while(t){l=h,h=[];while(++p<t)l&&l[p].run();p=-1,t=h.length}l=null,c=!1,f(e)}}function g(e,t){this.fun=e,this.array=t}function m(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];h.push(new g(e,t)),1!==h.length||c||u(y)},g.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=m,o.addListener=m,o.once=m,o.off=m,o.removeListener=m,o.removeAllListeners=m,o.emit=m,o.prependListener=m,o.prependOnceListener=m,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],51:[function(e,t,r){(function(e){(function(n){var i="object"==typeof r&&r&&!r.nodeType&&r,o="object"==typeof t&&t&&!t.nodeType&&t,s="object"==typeof e&&e;s.global!==s&&s.window!==s&&s.self!==s||(n=s);var a,u,f=2147483647,l=36,h=1,c=26,p=38,d=700,y=72,g=128,m="-",w=/^xn--/,b=/[^\x20-\x7E]/,v=/[\x2E\u3002\uFF0E\uFF61]/g,E={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},T=l-h,_=Math.floor,S=String.fromCharCode;function R(e){throw new RangeError(E[e])}function C(e,t){var r=e.length,n=[];while(r--)n[r]=t(e[r]);return n}function L(e,t){var r=e.split("@"),n="";r.length>1&&(n=r[0]+"@",e=r[1]),e=e.replace(v,".");var i=e.split("."),o=C(i,t).join(".");return n+o}function I(e){var t,r,n=[],i=0,o=e.length;while(i<o)t=e.charCodeAt(i++),t>=55296&&t<=56319&&i<o?(r=e.charCodeAt(i++),56320==(64512&r)?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--)):n.push(t);return n}function A(e){return C(e,(function(e){var t="";return e>65535&&(e-=65536,t+=S(e>>>10&1023|55296),e=56320|1023&e),t+=S(e),t})).join("")}function B(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:l}function x(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function N(e,t,r){var n=0;for(e=r?_(e/d):e>>1,e+=_(e/t);e>T*c>>1;n+=l)e=_(e/T);return _(n+(T+1)*e/(e+p))}function P(e){var t,r,n,i,o,s,a,u,p,d,w=[],b=e.length,v=0,E=g,T=y;for(r=e.lastIndexOf(m),r<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&R("not-basic"),w.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<b;){for(o=v,s=1,a=l;;a+=l){if(i>=b&&R("invalid-input"),u=B(e.charCodeAt(i++)),(u>=l||u>_((f-v)/s))&&R("overflow"),v+=u*s,p=a<=T?h:a>=T+c?c:a-T,u<p)break;d=l-p,s>_(f/d)&&R("overflow"),s*=d}t=w.length+1,T=N(v-o,t,0==o),_(v/t)>f-E&&R("overflow"),E+=_(v/t),v%=t,w.splice(v++,0,E)}return A(w)}function O(e){var t,r,n,i,o,s,a,u,p,d,w,b,v,E,T,C=[];for(e=I(e),b=e.length,t=g,r=0,o=y,s=0;s<b;++s)w=e[s],w<128&&C.push(S(w));n=i=C.length,i&&C.push(m);while(n<b){for(a=f,s=0;s<b;++s)w=e[s],w>=t&&w<a&&(a=w);for(v=n+1,a-t>_((f-r)/v)&&R("overflow"),r+=(a-t)*v,t=a,s=0;s<b;++s)if(w=e[s],w<t&&++r>f&&R("overflow"),w==t){for(u=r,p=l;;p+=l){if(d=p<=o?h:p>=o+c?c:p-o,u<d)break;T=u-d,E=l-d,C.push(S(x(d+T%E,0))),u=_(T/E)}C.push(S(x(u,0))),o=N(r,v,n==i),r=0,++n}++r,++t}return C.join("")}function U(e){return L(e,(function(e){return w.test(e)?P(e.slice(4).toLowerCase()):e}))}function k(e){return L(e,(function(e){return b.test(e)?"xn--"+O(e):e}))}if(a={version:"1.4.1",ucs2:{decode:I,encode:A},decode:P,encode:O,toASCII:k,toUnicode:U},"function"==typeof define&&"object"==typeof define.amd&&define.amd)define("punycode",(function(){return a}));else if(i&&o)if(t.exports==i)o.exports=a;else for(u in a)a.hasOwnProperty(u)&&(i[u]=a[u]);else n.punycode=a})(this)}).call(this,"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{}],52:[function(e,t,r){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,o){t=t||"&",r=r||"=";var s={};if("string"!==typeof e||0===e.length)return s;var a=/\+/g;e=e.split(t);var u=1e3;o&&"number"===typeof o.maxKeys&&(u=o.maxKeys);var f=e.length;u>0&&f>u&&(f=u);for(var l=0;l<f;++l){var h,c,p,d,y=e[l].replace(a,"%20"),g=y.indexOf(r);g>=0?(h=y.substr(0,g),c=y.substr(g+1)):(h=y,c=""),p=decodeURIComponent(h),d=decodeURIComponent(c),n(s,p)?i(s[p])?s[p].push(d):s[p]=[s[p],d]:s[p]=d}return s};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{}],53:[function(e,t,r){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,a){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"===typeof e?o(s(e),(function(s){var a=encodeURIComponent(n(s))+r;return i(e[s])?o(e[s],(function(e){return a+encodeURIComponent(n(e))})).join(t):a+encodeURIComponent(n(e[s]))})).join(t):a?encodeURIComponent(n(a))+r+encodeURIComponent(n(e)):""};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function o(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}var s=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}},{}],54:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode"),r.encode=r.stringify=e("./encode")},{"./decode":52,"./encode":53}],55:[function(e,t,r){t.exports=e("./lib/_stream_duplex.js")},{"./lib/_stream_duplex.js":56}],56:[function(e,t,r){"use strict";var n=e("process-nextick-args"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=h;var o=e("core-util-is");o.inherits=e("inherits");var s=e("./_stream_readable"),a=e("./_stream_writable");o.inherits(h,s);for(var u=i(a.prototype),f=0;f<u.length;f++){var l=u[f];h.prototype[l]||(h.prototype[l]=a.prototype[l])}function h(e){if(!(this instanceof h))return new h(e);s.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",c)}function c(){this.allowHalfOpen||this._writableState.ended||n(p,this)}function p(e){e.end()}Object.defineProperty(h.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),h.prototype._destroy=function(e,t){this.push(null),this.end(),n(t,e)}},{"./_stream_readable":58,"./_stream_writable":60,"core-util-is":42,inherits:46,"process-nextick-args":49}],57:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_transform"),i=e("core-util-is");function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":59,"core-util-is":42,inherits:46}],58:[function(e,t,r){(function(r,n){"use strict";var i=e("process-nextick-args");t.exports=T;var o,s=e("isarray");T.ReadableState=E;e("events").EventEmitter;var a=function(e,t){return e.listeners(t).length},u=e("./internal/streams/stream"),f=e("safe-buffer").Buffer,l=n.Uint8Array||function(){};function h(e){return f.from(e)}function c(e){return f.isBuffer(e)||e instanceof l}var p=e("core-util-is");p.inherits=e("inherits");var d=e("util"),y=void 0;y=d&&d.debuglog?d.debuglog("stream"):function(){};var g,m=e("./internal/streams/BufferList"),w=e("./internal/streams/destroy");p.inherits(T,u);var b=["error","close","destroy","pause","resume"];function v(e,t,r){if("function"===typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}function E(t,r){o=o||e("./_stream_duplex"),t=t||{},this.objectMode=!!t.objectMode,r instanceof o&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var n=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new m,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(g||(g=e("string_decoder/").StringDecoder),this.decoder=new g(t.encoding),this.encoding=t.encoding)}function T(t){if(o=o||e("./_stream_duplex"),!(this instanceof T))return new T(t);this._readableState=new E(t,this),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),u.call(this)}function _(e,t,r,n,i){var o,s=e._readableState;null===t?(s.reading=!1,B(e,s)):(i||(o=R(s,t)),o?e.emit("error",o):s.objectMode||t&&t.length>0?("string"===typeof t||s.objectMode||Object.getPrototypeOf(t)===f.prototype||(t=h(t)),n?s.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):S(e,s,t,!0):s.ended?e.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?S(e,s,t,!1):P(e,s)):S(e,s,t,!1))):n||(s.reading=!1));return C(s)}function S(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&x(e)),P(e,t)}function R(e,t){var r;return c(t)||"string"===typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk")),r}function C(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}Object.defineProperty(T.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),T.prototype.destroy=w.destroy,T.prototype._undestroy=w.undestroy,T.prototype._destroy=function(e,t){this.push(null),t(e)},T.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof e&&(t=t||n.defaultEncoding,t!==n.encoding&&(e=f.from(e,t),t=""),r=!0),_(this,e,t,!1,r)},T.prototype.unshift=function(e){return _(this,e,null,!0,!1)},T.prototype.isPaused=function(){return!1===this._readableState.flowing},T.prototype.setEncoding=function(t){return g||(g=e("string_decoder/").StringDecoder),this._readableState.decoder=new g(t),this._readableState.encoding=t,this};var L=8388608;function I(e){return e>=L?e=L:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function A(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=I(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function B(e,t){if(!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,x(e)}}function x(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(y("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?i(N,e):N(e))}function N(e){y("emit readable"),e.emit("readable"),V(e)}function P(e,t){t.readingMore||(t.readingMore=!0,i(O,e,t))}function O(e,t){var r=t.length;while(!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark){if(y("maybeReadMore read 0"),e.read(0),r===t.length)break;r=t.length}t.readingMore=!1}function U(e){return function(){var t=e._readableState;y("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,V(e))}}function k(e){y("readable nexttick read 0"),e.read(0)}function M(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i(D,e,t))}function D(e,t){t.reading||(y("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),V(e),t.flowing&&!t.reading&&e.read(0)}function V(e){var t=e._readableState;y("flow",t.flowing);while(t.flowing&&null!==e.read());}function q(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=F(e,t.buffer,t.decoder),r);var r}function F(e,t,r){var n;return e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?j(e,t):Y(e,t),n}function j(e,t){var r=t.head,n=1,i=r.data;e-=i.length;while(r=r.next){var o=r.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),e-=s,0===e){s===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(s));break}++n}return t.length-=n,i}function Y(e,t){var r=f.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;while(n=n.next){var o=n.data,s=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,s),e-=s,0===e){s===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(s));break}++i}return t.length-=i,r}function H(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,i(G,t,e))}function G(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function z(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}T.prototype.read=function(e){y("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return y("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?H(this):x(this),null;if(e=A(e,t),0===e&&t.ended)return 0===t.length&&H(this),null;var n,i=t.needReadable;return y("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&(i=!0,y("length less than watermark",i)),t.ended||t.reading?(i=!1,y("reading or ended",i)):i&&(y("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=A(r,t))),n=e>0?q(e,t):null,null===n?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&H(this)),null!==n&&this.emit("data",n),n},T.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},T.prototype.pipe=function(e,t){var n=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e);break}o.pipesCount+=1,y("pipe count=%d opts=%j",o.pipesCount,t);var s=(!t||!1!==t.end)&&e!==r.stdout&&e!==r.stderr,u=s?l:E;function f(e,t){y("onunpipe"),e===n&&t&&!1===t.hasUnpiped&&(t.hasUnpiped=!0,p())}function l(){y("onend"),e.end()}o.endEmitted?i(u):n.once("end",u),e.on("unpipe",f);var h=U(n);e.on("drain",h);var c=!1;function p(){y("cleanup"),e.removeListener("close",w),e.removeListener("finish",b),e.removeListener("drain",h),e.removeListener("error",m),e.removeListener("unpipe",f),n.removeListener("end",l),n.removeListener("end",E),n.removeListener("data",g),c=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||h()}var d=!1;function g(t){y("ondata"),d=!1;var r=e.write(t);!1!==r||d||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==z(o.pipes,e))&&!c&&(y("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,d=!0),n.pause())}function m(t){y("onerror",t),E(),e.removeListener("error",m),0===a(e,"error")&&e.emit("error",t)}function w(){e.removeListener("finish",b),E()}function b(){y("onfinish"),e.removeListener("close",w),E()}function E(){y("unpipe"),n.unpipe(e)}return n.on("data",g),v(e,"error",m),e.once("close",w),e.once("finish",b),e.emit("pipe",n),o.flowing||(y("pipe resume"),n.resume()),e},T.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var s=z(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},T.prototype.on=function(e,t){var r=u.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&x(this):i(k,this))}return r},T.prototype.addListener=T.prototype.on,T.prototype.resume=function(){var e=this._readableState;return e.flowing||(y("resume"),e.flowing=!0,M(this,e)),this},T.prototype.pause=function(){return y("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(y("pause"),this._readableState.flowing=!1,this.emit("pause")),this},T.prototype.wrap=function(e){var t=this._readableState,r=!1,n=this;for(var i in e.on("end",(function(){if(y("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&n.push(e)}n.push(null)})),e.on("data",(function(i){if(y("wrapped data"),t.decoder&&(i=t.decoder.write(i)),(!t.objectMode||null!==i&&void 0!==i)&&(t.objectMode||i&&i.length)){var o=n.push(i);o||(r=!0,e.pause())}})),e)void 0===this[i]&&"function"===typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<b.length;o++)e.on(b[o],n.emit.bind(n,b[o]));return n._read=function(t){y("wrapped _read",t),r&&(r=!1,e.resume())},n},T._fromList=q}).call(this,e("_process"),"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{"./_stream_duplex":56,"./internal/streams/BufferList":61,"./internal/streams/destroy":62,"./internal/streams/stream":63,_process:50,"core-util-is":42,events:43,inherits:46,isarray:48,"process-nextick-args":49,"safe-buffer":68,"string_decoder/":74,util:39}],59:[function(e,t,r){"use strict";t.exports=a;var n=e("./_stream_duplex"),i=e("core-util-is");function o(e){this.afterTransform=function(t,r){return s(e,t,r)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function s(e,t,r){var n=e._transformState;n.transforming=!1;var i=n.writecb;if(!i)return e.emit("error",new Error("write callback called multiple times"));n.writechunk=null,n.writecb=null,null!==r&&void 0!==r&&e.push(r),i(t);var o=e._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&e._read(o.highWaterMark)}function a(e){if(!(this instanceof a))return new a(e);n.call(this,e),this._transformState=new o(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"===typeof e.transform&&(this._transform=e.transform),"function"===typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",(function(){"function"===typeof this._flush?this._flush((function(e,r){u(t,e,r)})):u(t)}))}function u(e,t,r){if(t)return e.emit("error",t);null!==r&&void 0!==r&&e.push(r);var n=e._writableState,i=e._transformState;if(n.length)throw new Error("Calling transform done when ws.length != 0");if(i.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=e("inherits"),i.inherits(a,n),a.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},a.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},a.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},a.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},a.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit("close")}))}},{"./_stream_duplex":56,"core-util-is":42,inherits:46}],60:[function(e,t,r){(function(r,n){"use strict";var i=e("process-nextick-args");function o(e){var t=this;this.next=null,this.entry=null,this.finish=function(){k(t,e)}}t.exports=b;var s,a=!r.browser&&["v0.10","v0.9."].indexOf(r.version.slice(0,5))>-1?setImmediate:i;b.WritableState=w;var u=e("core-util-is");u.inherits=e("inherits");var f={deprecate:e("util-deprecate")},l=e("./internal/streams/stream"),h=e("safe-buffer").Buffer,c=n.Uint8Array||function(){};function p(e){return h.from(e)}function d(e){return h.isBuffer(e)||e instanceof c}var y,g=e("./internal/streams/destroy");function m(){}function w(t,r){s=s||e("./_stream_duplex"),t=t||{},this.objectMode=!!t.objectMode,r instanceof s&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var n=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var a=!1===t.decodeStrings;this.decodeStrings=!a,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){L(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function b(t){if(s=s||e("./_stream_duplex"),!y.call(b,this)&&!(this instanceof s))return new b(t);this._writableState=new w(t,this),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),l.call(this)}function v(e,t){var r=new Error("write after end");e.emit("error",r),i(t,r)}function E(e,t,r,n){var o=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"===typeof r||void 0===r||t.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(e.emit("error",s),i(n,s),o=!1),o}function T(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!==typeof t||(t=h.from(t,r)),t}function _(e,t,r,n,i,o){if(!r){var s=T(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var f=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},f?f.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else S(e,t,!1,a,n,i,o);return u}function S(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function R(e,t,r,n,o){--t.pendingcb,r?(i(o,n),i(O,e,t),e._writableState.errorEmitted=!0,e.emit("error",n)):(o(n),e._writableState.errorEmitted=!0,e.emit("error",n),O(e,t))}function C(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function L(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(C(r),t)R(e,r,n,t,i);else{var o=x(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||B(e,r),n?a(I,e,r,o,i):I(e,r,o,i)}}function I(e,t,r,n){r||A(e,t),t.pendingcb--,n(),O(e,t)}function A(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function B(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),s=t.corkedRequestsFree;s.entry=r;var a=0,u=!0;while(r)i[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;i.allBuffers=u,S(e,t,!0,t.length,i,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new o(t)}else{while(r){var f=r.chunk,l=r.encoding,h=r.callback,c=t.objectMode?1:f.length;if(S(e,t,!1,c,f,l,h),r=r.next,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=r,t.bufferProcessing=!1}function x(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function N(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),O(e,t)}))}function P(e,t){t.prefinished||t.finalCalled||("function"===typeof e._final?(t.pendingcb++,t.finalCalled=!0,i(N,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function O(e,t){var r=x(t);return r&&(P(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}function U(e,t,r){t.ending=!0,O(e,t),r&&(t.finished?i(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function k(e,t,r){var n=e.entry;e.entry=null;while(n){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}u.inherits(b,l),w.prototype.getBuffer=function(){var e=this.bufferedRequest,t=[];while(e)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(w.prototype,"buffer",{get:f.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(y=Function.prototype[Symbol.hasInstance],Object.defineProperty(b,Symbol.hasInstance,{value:function(e){return!!y.call(this,e)||e&&e._writableState instanceof w}})):y=function(e){return e instanceof this},b.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},b.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=d(e)&&!n.objectMode;return o&&!h.isBuffer(e)&&(e=p(e)),"function"===typeof t&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),"function"!==typeof r&&(r=m),n.ended?v(this,r):(o||E(this,n,e,r))&&(n.pendingcb++,i=_(this,n,o,e,t,r)),i},b.prototype.cork=function(){var e=this._writableState;e.corked++},b.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||B(this,e))},b.prototype.setDefaultEncoding=function(e){if("string"===typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},b.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},b.prototype._writev=null,b.prototype.end=function(e,t,r){var n=this._writableState;"function"===typeof e?(r=e,e=null,t=null):"function"===typeof t&&(r=t,t=null),null!==e&&void 0!==e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||U(this,n,r)},Object.defineProperty(b.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),b.prototype.destroy=g.destroy,b.prototype._undestroy=g.undestroy,b.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,e("_process"),"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{"./_stream_duplex":56,"./internal/streams/destroy":62,"./internal/streams/stream":63,_process:50,"core-util-is":42,inherits:46,"process-nextick-args":49,"safe-buffer":68,"util-deprecate":78}],61:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=e("safe-buffer").Buffer;function o(e,t,r){e.copy(t,r)}t.exports=function(){function e(){n(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";var t=this.head,r=""+t.data;while(t=t.next)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return i.alloc(0);if(1===this.length)return this.head.data;var t=i.allocUnsafe(e>>>0),r=this.head,n=0;while(r)o(r.data,t,n),n+=r.data.length,r=r.next;return t},e}()},{"safe-buffer":68}],62:[function(e,t,r){"use strict";var n=e("process-nextick-args");function i(e,t){var r=this,i=this._readableState&&this._readableState.destroyed,o=this._writableState&&this._writableState.destroyed;i||o?t?t(e):!e||this._writableState&&this._writableState.errorEmitted||n(s,this,e):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?(n(s,r,e),r._writableState&&(r._writableState.errorEmitted=!0)):t&&t(e)})))}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function s(e,t){e.emit("error",t)}t.exports={destroy:i,undestroy:o}},{"process-nextick-args":49}],63:[function(e,t,r){t.exports=e("events").EventEmitter},{events:43}],64:[function(e,t,r){t.exports=e("./readable").PassThrough},{"./readable":65}],65:[function(e,t,r){r=t.exports=e("./lib/_stream_readable.js"),r.Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":56,"./lib/_stream_passthrough.js":57,"./lib/_stream_readable.js":58,"./lib/_stream_transform.js":59,"./lib/_stream_writable.js":60}],66:[function(e,t,r){t.exports=e("./readable").Transform},{"./readable":65}],67:[function(e,t,r){t.exports=e("./lib/_stream_writable.js")},{"./lib/_stream_writable.js":60}],68:[function(e,t,r){var n=e("buffer"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,r),r.Buffer=s),o(i,s),s.from=function(e,t,r){if("number"===typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},s.alloc=function(e,t,r){if("number"!==typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"===typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if("number"!==typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!==typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},{buffer:40}],69:[function(e,t,r){t.exports=o;var n=e("events").EventEmitter,i=e("inherits");function o(){n.call(this)}i(o,n),o.Readable=e("readable-stream/readable.js"),o.Writable=e("readable-stream/writable.js"),o.Duplex=e("readable-stream/duplex.js"),o.Transform=e("readable-stream/transform.js"),o.PassThrough=e("readable-stream/passthrough.js"),o.Stream=o,o.prototype.pipe=function(e,t){var r=this;function i(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function o(){r.readable&&r.resume&&r.resume()}r.on("data",i),e.on("drain",o),e._isStdio||t&&!1===t.end||(r.on("end",a),r.on("close",u));var s=!1;function a(){s||(s=!0,e.end())}function u(){s||(s=!0,"function"===typeof e.destroy&&e.destroy())}function f(e){if(l(),0===n.listenerCount(this,"error"))throw e}function l(){r.removeListener("data",i),e.removeListener("drain",o),r.removeListener("end",a),r.removeListener("close",u),r.removeListener("error",f),e.removeListener("error",f),r.removeListener("end",l),r.removeListener("close",l),e.removeListener("close",l)}return r.on("error",f),e.on("error",f),r.on("end",l),r.on("close",l),e.on("close",l),e.emit("pipe",r),e}},{events:43,inherits:46,"readable-stream/duplex.js":55,"readable-stream/passthrough.js":64,"readable-stream/readable.js":65,"readable-stream/transform.js":66,"readable-stream/writable.js":67}],70:[function(e,t,r){(function(t){var n=e("./lib/request"),i=e("xtend"),o=e("builtin-status-codes"),s=e("url"),a=r;a.request=function(e,r){e="string"===typeof e?s.parse(e):i(e);var o=-1===t.location.protocol.search(/^https?:$/)?"http:":"",a=e.protocol||o,u=e.hostname||e.host,f=e.port,l=e.path||"/";u&&-1!==u.indexOf(":")&&(u="["+u+"]"),e.url=(u?a+"//"+u:"")+(f?":"+f:"")+l,e.method=(e.method||"GET").toUpperCase(),e.headers=e.headers||{};var h=new n(e);return r&&h.on("response",r),h},a.get=function(e,t){var r=a.request(e,t);return r.end(),r},a.Agent=function(){},a.Agent.defaultMaxSockets=4,a.STATUS_CODES=o,a.METHODS=["CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REPORT","SEARCH","SUBSCRIBE","TRACE","UNLOCK","UNSUBSCRIBE"]}).call(this,"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{"./lib/request":72,"builtin-status-codes":41,url:76,xtend:82}],71:[function(e,t,r){(function(e){r.fetch=a(e.fetch)&&a(e.ReadableStream),r.blobConstructor=!1;try{new Blob([new ArrayBuffer(1)]),r.blobConstructor=!0}catch(u){}var t;function n(){if(void 0!==t)return t;if(e.XMLHttpRequest){t=new e.XMLHttpRequest;try{t.open("GET",e.XDomainRequest?"/":"https://example.com")}catch(u){t=null}}else t=null;return t}function i(e){var t=n();if(!t)return!1;try{return t.responseType=e,t.responseType===e}catch(u){}return!1}var o="undefined"!==typeof e.ArrayBuffer,s=o&&a(e.ArrayBuffer.prototype.slice);function a(e){return"function"===typeof e}r.arraybuffer=r.fetch||o&&i("arraybuffer"),r.msstream=!r.fetch&&s&&i("ms-stream"),r.mozchunkedarraybuffer=!r.fetch&&o&&i("moz-chunked-arraybuffer"),r.overrideMimeType=r.fetch||!!n()&&a(n().overrideMimeType),r.vbArray=a(e.VBArray),t=null}).call(this,"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{}],72:[function(e,t,r){(function(r,n,i){var o=e("./capability"),s=e("inherits"),a=e("./response"),u=e("readable-stream"),f=e("to-arraybuffer"),l=a.IncomingMessage,h=a.readyStates;function c(e,t){return o.fetch&&t?"fetch":o.mozchunkedarraybuffer?"moz-chunked-arraybuffer":o.msstream?"ms-stream":o.arraybuffer&&e?"arraybuffer":o.vbArray&&e?"text:vbarray":"text"}var p=t.exports=function(e){var t,r=this;u.Writable.call(r),r._opts=e,r._body=[],r._headers={},e.auth&&r.setHeader("Authorization","Basic "+new i(e.auth).toString("base64")),Object.keys(e.headers).forEach((function(t){r.setHeader(t,e.headers[t])}));var n=!0;if("disable-fetch"===e.mode||"timeout"in e)n=!1,t=!0;else if("prefer-streaming"===e.mode)t=!1;else if("allow-wrong-content-type"===e.mode)t=!o.overrideMimeType;else{if(e.mode&&"default"!==e.mode&&"prefer-fast"!==e.mode)throw new Error("Invalid value for opts.mode");t=!0}r._mode=c(t,n),r.on("finish",(function(){r._onFinish()}))};function d(e){try{var t=e.status;return null!==t&&0!==t}catch(r){return!1}}s(p,u.Writable),p.prototype.setHeader=function(e,t){var r=this,n=e.toLowerCase();-1===y.indexOf(n)&&(r._headers[n]={name:e,value:t})},p.prototype.getHeader=function(e){var t=this._headers[e.toLowerCase()];return t?t.value:null},p.prototype.removeHeader=function(e){var t=this;delete t._headers[e.toLowerCase()]},p.prototype._onFinish=function(){var e=this;if(!e._destroyed){var t=e._opts,s=e._headers,a=null;"GET"!==t.method&&"HEAD"!==t.method&&(a=o.blobConstructor?new n.Blob(e._body.map((function(e){return f(e)})),{type:(s["content-type"]||{}).value||""}):i.concat(e._body).toString());var u=[];if(Object.keys(s).forEach((function(e){var t=s[e].name,r=s[e].value;Array.isArray(r)?r.forEach((function(e){u.push([t,e])})):u.push([t,r])})),"fetch"===e._mode)n.fetch(e._opts.url,{method:e._opts.method,headers:u,body:a||void 0,mode:"cors",credentials:t.withCredentials?"include":"same-origin"}).then((function(t){e._fetchResponse=t,e._connect()}),(function(t){e.emit("error",t)}));else{var l=e._xhr=new n.XMLHttpRequest;try{l.open(e._opts.method,e._opts.url,!0)}catch(c){return void r.nextTick((function(){e.emit("error",c)}))}"responseType"in l&&(l.responseType=e._mode.split(":")[0]),"withCredentials"in l&&(l.withCredentials=!!t.withCredentials),"text"===e._mode&&"overrideMimeType"in l&&l.overrideMimeType("text/plain; charset=x-user-defined"),"timeout"in t&&(l.timeout=t.timeout,l.ontimeout=function(){e.emit("timeout")}),u.forEach((function(e){l.setRequestHeader(e[0],e[1])})),e._response=null,l.onreadystatechange=function(){switch(l.readyState){case h.LOADING:case h.DONE:e._onXHRProgress();break}},"moz-chunked-arraybuffer"===e._mode&&(l.onprogress=function(){e._onXHRProgress()}),l.onerror=function(){e._destroyed||e.emit("error",new Error("XHR error"))};try{l.send(a)}catch(c){return void r.nextTick((function(){e.emit("error",c)}))}}}},p.prototype._onXHRProgress=function(){var e=this;d(e._xhr)&&!e._destroyed&&(e._response||e._connect(),e._response._onXHRProgress())},p.prototype._connect=function(){var e=this;e._destroyed||(e._response=new l(e._xhr,e._fetchResponse,e._mode),e._response.on("error",(function(t){e.emit("error",t)})),e.emit("response",e._response))},p.prototype._write=function(e,t,r){var n=this;n._body.push(e),r()},p.prototype.abort=p.prototype.destroy=function(){var e=this;e._destroyed=!0,e._response&&(e._response._destroyed=!0),e._xhr&&e._xhr.abort()},p.prototype.end=function(e,t,r){var n=this;"function"===typeof e&&(r=e,e=void 0),u.Writable.prototype.end.call(n,e,t,r)},p.prototype.flushHeaders=function(){},p.prototype.setTimeout=function(){},p.prototype.setNoDelay=function(){},p.prototype.setSocketKeepAlive=function(){};var y=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"]}).call(this,e("_process"),"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{},e("buffer").Buffer)},{"./capability":71,"./response":73,_process:50,buffer:40,inherits:46,"readable-stream":65,"to-arraybuffer":75}],73:[function(e,t,r){(function(t,n,i){var o=e("./capability"),s=e("inherits"),a=e("readable-stream"),u=r.readyStates={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},f=r.IncomingMessage=function(e,r,n){var s=this;if(a.Readable.call(s),s._mode=n,s.headers={},s.rawHeaders=[],s.trailers={},s.rawTrailers=[],s.on("end",(function(){t.nextTick((function(){s.emit("close")}))})),"fetch"===n){s._fetchResponse=r,s.url=r.url,s.statusCode=r.status,s.statusMessage=r.statusText,r.headers.forEach((function(e,t){s.headers[t.toLowerCase()]=e,s.rawHeaders.push(t,e)}));var u=r.body.getReader();function f(){u.read().then((function(e){s._destroyed||(e.done?s.push(null):(s.push(new i(e.value)),f()))})).catch((function(e){s.emit("error",e)}))}f()}else{s._xhr=e,s._pos=0,s.url=e.responseURL,s.statusCode=e.status,s.statusMessage=e.statusText;var l=e.getAllResponseHeaders().split(/\r?\n/);if(l.forEach((function(e){var t=e.match(/^([^:]+):\s*(.*)/);if(t){var r=t[1].toLowerCase();"set-cookie"===r?(void 0===s.headers[r]&&(s.headers[r]=[]),s.headers[r].push(t[2])):void 0!==s.headers[r]?s.headers[r]+=", "+t[2]:s.headers[r]=t[2],s.rawHeaders.push(t[1],t[2])}})),s._charset="x-user-defined",!o.overrideMimeType){var h=s.rawHeaders["mime-type"];if(h){var c=h.match(/;\s*charset=([^;])(;|$)/);c&&(s._charset=c[1].toLowerCase())}s._charset||(s._charset="utf-8")}}};s(f,a.Readable),f.prototype._read=function(){},f.prototype._onXHRProgress=function(){var e=this,t=e._xhr,r=null;switch(e._mode){case"text:vbarray":if(t.readyState!==u.DONE)break;try{r=new n.VBArray(t.responseBody).toArray()}catch(l){}if(null!==r){e.push(new i(r));break}case"text":try{r=t.responseText}catch(l){e._mode="text:vbarray";break}if(r.length>e._pos){var o=r.substr(e._pos);if("x-user-defined"===e._charset){for(var s=new i(o.length),a=0;a<o.length;a++)s[a]=255&o.charCodeAt(a);e.push(s)}else e.push(o,e._charset);e._pos=r.length}break;case"arraybuffer":if(t.readyState!==u.DONE||!t.response)break;r=t.response,e.push(new i(new Uint8Array(r)));break;case"moz-chunked-arraybuffer":if(r=t.response,t.readyState!==u.LOADING||!r)break;e.push(new i(new Uint8Array(r)));break;case"ms-stream":if(r=t.response,t.readyState!==u.LOADING)break;var f=new n.MSStreamReader;f.onprogress=function(){f.result.byteLength>e._pos&&(e.push(new i(new Uint8Array(f.result.slice(e._pos)))),e._pos=f.result.byteLength)},f.onload=function(){e.push(null)},f.readAsArrayBuffer(r);break}e._xhr.readyState===u.DONE&&"ms-stream"!==e._mode&&e.push(null)}}).call(this,e("_process"),"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{},e("buffer").Buffer)},{"./capability":71,_process:50,buffer:40,inherits:46,"readable-stream":65}],74:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=n.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){if(!e)return"utf8";var t;while(1)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function s(e){var t=o(e);if("string"!==typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}function a(e){var t;switch(this.encoding=s(e),this.encoding){case"utf16le":this.text=d,this.end=y,t=4;break;case"utf8":this.fillLast=h,t=4;break;case"base64":this.text=g,this.end=m,t=3;break;default:return this.write=w,void(this.end=b)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function u(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:-1}function f(e,t,r){var n=t.length-1;if(n<r)return 0;var i=u(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r?0:(i=u(t[n]),i>=0?(i>0&&(e.lastNeed=i-2),i):--n<r?0:(i=u(t[n]),i>=0?(i>0&&(2===i?i=0:e.lastNeed=i-3),i):0))}function l(e,t,r){if(128!==(192&t[0]))return e.lastNeed=0,"�".repeat(r);if(e.lastNeed>1&&t.length>1){if(128!==(192&t[1]))return e.lastNeed=1,"�".repeat(r+1);if(e.lastNeed>2&&t.length>2&&128!==(192&t[2]))return e.lastNeed=2,"�".repeat(r+2)}}function h(e){var t=this.lastTotal-this.lastNeed,r=l(this,e,t);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function c(e,t){var r=f(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)}function p(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�".repeat(this.lastTotal-this.lastNeed):t}function d(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function y(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function g(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function m(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function w(e){return e.toString(this.encoding)}function b(e){return e&&e.length?this.write(e):""}r.StringDecoder=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(t=this.fillLast(e),void 0===t)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=p,a.prototype.text=c,a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},{"safe-buffer":68}],75:[function(e,t,r){var n=e("buffer").Buffer;t.exports=function(e){if(e instanceof Uint8Array){if(0===e.byteOffset&&e.byteLength===e.buffer.byteLength)return e.buffer;if("function"===typeof e.buffer.slice)return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}if(n.isBuffer(e)){for(var t=new Uint8Array(e.length),r=e.length,i=0;i<r;i++)t[i]=e[i];return t.buffer}throw new Error("Argument must be a Buffer")}},{buffer:40}],76:[function(e,t,r){"use strict";var n=e("punycode"),i=e("./util");function o(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}r.parse=E,r.resolve=_,r.resolveObject=S,r.format=T,r.Url=o;var s=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,u=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,f=["<",">",'"',"`"," ","\r","\n","\t"],l=["{","}","|","\\","^","`"].concat(f),h=["'"].concat(l),c=["%","/","?",";","#"].concat(h),p=["/","?","#"],d=255,y=/^[+a-z0-9A-Z_-]{0,63}$/,g=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,m={javascript:!0,"javascript:":!0},w={javascript:!0,"javascript:":!0},b={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=e("querystring");function E(e,t,r){if(e&&i.isObject(e)&&e instanceof o)return e;var n=new o;return n.parse(e,t,r),n}function T(e){return i.isString(e)&&(e=E(e)),e instanceof o?e.format():o.prototype.format.call(e)}function _(e,t){return E(e,!1,!0).resolve(t)}function S(e,t){return e?E(e,!1,!0).resolveObject(t):t}o.prototype.parse=function(e,t,r){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var o=e.indexOf("?"),a=-1!==o&&o<e.indexOf("#")?"?":"#",f=e.split(a),l=/\\/g;f[0]=f[0].replace(l,"/"),e=f.join(a);var E=e;if(E=E.trim(),!r&&1===e.split("#").length){var T=u.exec(E);if(T)return this.path=E,this.href=E,this.pathname=T[1],T[2]?(this.search=T[2],this.query=t?v.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var _=s.exec(E);if(_){_=_[0];var S=_.toLowerCase();this.protocol=S,E=E.substr(_.length)}if(r||_||E.match(/^\/\/[^@\/]+@[^@\/]+/)){var R="//"===E.substr(0,2);!R||_&&w[_]||(E=E.substr(2),this.slashes=!0)}if(!w[_]&&(R||_&&!b[_])){for(var C,L,I=-1,A=0;A<p.length;A++){var B=E.indexOf(p[A]);-1!==B&&(-1===I||B<I)&&(I=B)}L=-1===I?E.lastIndexOf("@"):E.lastIndexOf("@",I),-1!==L&&(C=E.slice(0,L),E=E.slice(L+1),this.auth=decodeURIComponent(C)),I=-1;for(A=0;A<c.length;A++){B=E.indexOf(c[A]);-1!==B&&(-1===I||B<I)&&(I=B)}-1===I&&(I=E.length),this.host=E.slice(0,I),E=E.slice(I),this.parseHost(),this.hostname=this.hostname||"";var x="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!x)for(var N=this.hostname.split(/\./),P=(A=0,N.length);A<P;A++){var O=N[A];if(O&&!O.match(y)){for(var U="",k=0,M=O.length;k<M;k++)O.charCodeAt(k)>127?U+="x":U+=O[k];if(!U.match(y)){var D=N.slice(0,A),V=N.slice(A+1),q=O.match(g);q&&(D.push(q[1]),V.unshift(q[2])),V.length&&(E="/"+V.join(".")+E),this.hostname=D.join(".");break}}}this.hostname.length>d?this.hostname="":this.hostname=this.hostname.toLowerCase(),x||(this.hostname=n.toASCII(this.hostname));var F=this.port?":"+this.port:"",j=this.hostname||"";this.host=j+F,this.href+=this.host,x&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==E[0]&&(E="/"+E))}if(!m[S])for(A=0,P=h.length;A<P;A++){var Y=h[A];if(-1!==E.indexOf(Y)){var H=encodeURIComponent(Y);H===Y&&(H=escape(Y)),E=E.split(Y).join(H)}}var G=E.indexOf("#");-1!==G&&(this.hash=E.substr(G),E=E.slice(0,G));var z=E.indexOf("?");if(-1!==z?(this.search=E.substr(z),this.query=E.substr(z+1),t&&(this.query=v.parse(this.query)),E=E.slice(0,z)):t&&(this.search="",this.query={}),E&&(this.pathname=E),b[S]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){F=this.pathname||"";var W=this.search||"";this.path=F+W}return this.href=this.format(),this},o.prototype.format=function(){var e=this.auth||"";e&&(e=encodeURIComponent(e),e=e.replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",o=!1,s="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(s=v.stringify(this.query));var a=this.search||s&&"?"+s||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||b[t])&&!1!==o?(o="//"+(o||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):o||(o=""),n&&"#"!==n.charAt(0)&&(n="#"+n),a&&"?"!==a.charAt(0)&&(a="?"+a),r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})),a=a.replace("#","%23"),t+o+r+a+n},o.prototype.resolve=function(e){return this.resolveObject(E(e,!1,!0)).format()},o.prototype.resolveObject=function(e){if(i.isString(e)){var t=new o;t.parse(e,!1,!0),e=t}for(var r=new o,n=Object.keys(this),s=0;s<n.length;s++){var a=n[s];r[a]=this[a]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var u=Object.keys(e),f=0;f<u.length;f++){var l=u[f];"protocol"!==l&&(r[l]=e[l])}return b[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!b[e.protocol]){for(var h=Object.keys(e),c=0;c<h.length;c++){var p=h[c];r[p]=e[p]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||w[e.protocol])r.pathname=e.pathname;else{var d=(e.pathname||"").split("/");while(d.length&&!(e.host=d.shift()));e.host||(e.host=""),e.hostname||(e.hostname=""),""!==d[0]&&d.unshift(""),d.length<2&&d.unshift(""),r.pathname=d.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var y=r.pathname||"",g=r.search||"";r.path=y+g}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var m=r.pathname&&"/"===r.pathname.charAt(0),v=e.host||e.pathname&&"/"===e.pathname.charAt(0),E=v||m||r.host&&e.pathname,T=E,_=r.pathname&&r.pathname.split("/")||[],S=(d=e.pathname&&e.pathname.split("/")||[],r.protocol&&!b[r.protocol]);if(S&&(r.hostname="",r.port=null,r.host&&(""===_[0]?_[0]=r.host:_.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===d[0]?d[0]=e.host:d.unshift(e.host)),e.host=null),E=E&&(""===d[0]||""===_[0])),v)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,_=d;else if(d.length)_||(_=[]),_.pop(),_=_.concat(d),r.search=e.search,r.query=e.query;else if(!i.isNullOrUndefined(e.search)){if(S){r.hostname=r.host=_.shift();var R=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");R&&(r.auth=R.shift(),r.host=r.hostname=R.shift())}return r.search=e.search,r.query=e.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!_.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var C=_.slice(-1)[0],L=(r.host||e.host||_.length>1)&&("."===C||".."===C)||""===C,I=0,A=_.length;A>=0;A--)C=_[A],"."===C?_.splice(A,1):".."===C?(_.splice(A,1),I++):I&&(_.splice(A,1),I--);if(!E&&!T)for(;I--;I)_.unshift("..");!E||""===_[0]||_[0]&&"/"===_[0].charAt(0)||_.unshift(""),L&&"/"!==_.join("/").substr(-1)&&_.push("");var B=""===_[0]||_[0]&&"/"===_[0].charAt(0);if(S){r.hostname=r.host=B?"":_.length?_.shift():"";R=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");R&&(r.auth=R.shift(),r.host=r.hostname=R.shift())}return E=E||r.host&&_.length,E&&!B&&_.unshift(""),_.length?r.pathname=_.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},o.prototype.parseHost=function(){var e=this.host,t=a.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},{"./util":77,punycode:51,querystring:54}],77:[function(e,t,r){"use strict";t.exports={isString:function(e){return"string"===typeof e},isObject:function(e){return"object"===typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},{}],78:[function(e,t,r){(function(e){function r(e,t){if(n("noDeprecation"))return e;var r=!1;function i(){if(!r){if(n("throwDeprecation"))throw new Error(t);n("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}return i}function n(t){try{if(!e.localStorage)return!1}catch(n){return!1}var r=e.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=r}).call(this,"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{}],79:[function(e,t,r){arguments[4][46][0].apply(r,arguments)},{dup:46}],80:[function(e,t,r){t.exports=function(e){return e&&"object"===typeof e&&"function"===typeof e.copy&&"function"===typeof e.fill&&"function"===typeof e.readUInt8}},{}],81:[function(e,t,r){(function(t,n){var i=/%[sdj%]/g;r.format=function(e){if(!T(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(a(arguments[r]));return t.join(" ")}r=1;for(var n=arguments,o=n.length,s=String(e).replace(i,(function(e){if("%%"===e)return"%";if(r>=o)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(t){return"[Circular]"}default:return e}})),u=n[r];r<o;u=n[++r])b(u)||!C(u)?s+=" "+u:s+=" "+a(u);return s},r.deprecate=function(e,i){if(S(n.process))return function(){return r.deprecate(e,i).apply(this,arguments)};if(!0===t.noDeprecation)return e;var o=!1;function s(){if(!o){if(t.throwDeprecation)throw new Error(i);t.traceDeprecation?console.trace(i):console.error(i),o=!0}return e.apply(this,arguments)}return s};var o,s={};function a(e,t){var n={seen:[],stylize:f};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),w(t)?n.showHidden=t:t&&r._extend(n,t),S(n.showHidden)&&(n.showHidden=!1),S(n.depth)&&(n.depth=2),S(n.colors)&&(n.colors=!1),S(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=u),h(n,e,n.depth)}function u(e,t){var r=a.styles[t];return r?"["+a.colors[r][0]+"m"+e+"["+a.colors[r][1]+"m":e}function f(e,t){return e}function l(e){var t={};return e.forEach((function(e,r){t[e]=!0})),t}function h(e,t,n){if(e.customInspect&&t&&A(t.inspect)&&t.inspect!==r.inspect&&(!t.constructor||t.constructor.prototype!==t)){var i=t.inspect(n,e);return T(i)||(i=h(e,i,n)),i}var o=c(e,t);if(o)return o;var s=Object.keys(t),a=l(s);if(e.showHidden&&(s=Object.getOwnPropertyNames(t)),I(t)&&(s.indexOf("message")>=0||s.indexOf("description")>=0))return p(t);if(0===s.length){if(A(t)){var u=t.name?": "+t.name:"";return e.stylize("[Function"+u+"]","special")}if(R(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(L(t))return e.stylize(Date.prototype.toString.call(t),"date");if(I(t))return p(t)}var f,w="",b=!1,v=["{","}"];if(m(t)&&(b=!0,v=["[","]"]),A(t)){var E=t.name?": "+t.name:"";w=" [Function"+E+"]"}return R(t)&&(w=" "+RegExp.prototype.toString.call(t)),L(t)&&(w=" "+Date.prototype.toUTCString.call(t)),I(t)&&(w=" "+p(t)),0!==s.length||b&&0!=t.length?n<0?R(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),f=b?d(e,t,n,a,s):s.map((function(r){return y(e,t,n,a,r,b)})),e.seen.pop(),g(f,w,v)):v[0]+w+v[1]}function c(e,t){if(S(t))return e.stylize("undefined","undefined");if(T(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return E(t)?e.stylize(""+t,"number"):w(t)?e.stylize(""+t,"boolean"):b(t)?e.stylize("null","null"):void 0}function p(e){return"["+Error.prototype.toString.call(e)+"]"}function d(e,t,r,n,i){for(var o=[],s=0,a=t.length;s<a;++s)U(t,String(s))?o.push(y(e,t,r,n,String(s),!0)):o.push("");return i.forEach((function(i){i.match(/^\d+$/)||o.push(y(e,t,r,n,i,!0))})),o}function y(e,t,r,n,i,o){var s,a,u;if(u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]},u.get?a=u.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):u.set&&(a=e.stylize("[Setter]","special")),U(n,i)||(s="["+i+"]"),a||(e.seen.indexOf(u.value)<0?(a=b(r)?h(e,u.value,null):h(e,u.value,r-1),a.indexOf("\n")>-1&&(a=o?a.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+a.split("\n").map((function(e){return"   "+e})).join("\n"))):a=e.stylize("[Circular]","special")),S(s)){if(o&&i.match(/^\d+$/))return a;s=JSON.stringify(""+i),s.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=e.stylize(s,"string"))}return s+": "+a}function g(e,t,r){var n=e.reduce((function(e,t){return t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return n>60?r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]:r[0]+t+" "+e.join(", ")+" "+r[1]}function m(e){return Array.isArray(e)}function w(e){return"boolean"===typeof e}function b(e){return null===e}function v(e){return null==e}function E(e){return"number"===typeof e}function T(e){return"string"===typeof e}function _(e){return"symbol"===typeof e}function S(e){return void 0===e}function R(e){return C(e)&&"[object RegExp]"===x(e)}function C(e){return"object"===typeof e&&null!==e}function L(e){return C(e)&&"[object Date]"===x(e)}function I(e){return C(e)&&("[object Error]"===x(e)||e instanceof Error)}function A(e){return"function"===typeof e}function B(e){return null===e||"boolean"===typeof e||"number"===typeof e||"string"===typeof e||"symbol"===typeof e||"undefined"===typeof e}function x(e){return Object.prototype.toString.call(e)}function N(e){return e<10?"0"+e.toString(10):e.toString(10)}r.debuglog=function(e){if(S(o)&&(o=t.env.NODE_DEBUG||""),e=e.toUpperCase(),!s[e])if(new RegExp("\\b"+e+"\\b","i").test(o)){var n=t.pid;s[e]=function(){var t=r.format.apply(r,arguments);console.error("%s %d: %s",e,n,t)}}else s[e]=function(){};return s[e]},r.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},r.isArray=m,r.isBoolean=w,r.isNull=b,r.isNullOrUndefined=v,r.isNumber=E,r.isString=T,r.isSymbol=_,r.isUndefined=S,r.isRegExp=R,r.isObject=C,r.isDate=L,r.isError=I,r.isFunction=A,r.isPrimitive=B,r.isBuffer=e("./support/isBuffer");var P=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function O(){var e=new Date,t=[N(e.getHours()),N(e.getMinutes()),N(e.getSeconds())].join(":");return[e.getDate(),P[e.getMonth()],t].join(" ")}function U(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.log=function(){console.log("%s - %s",O(),r.format.apply(r,arguments))},r.inherits=e("inherits"),r._extend=function(e,t){if(!t||!C(t))return e;var r=Object.keys(t),n=r.length;while(n--)e[r[n]]=t[r[n]];return e}}).call(this,e("_process"),"undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{"./support/isBuffer":80,_process:50,inherits:79}],82:[function(e,t,r){t.exports=i;var n=Object.prototype.hasOwnProperty;function i(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var i in r)n.call(r,i)&&(e[i]=r[i])}return e}},{}]},{},[3]);