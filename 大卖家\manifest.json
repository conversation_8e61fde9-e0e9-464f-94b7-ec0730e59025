{"manifest_version": 3, "name": "大卖家跨境助手", "description": "大卖家,TEMU、跨境店铺精细化运营工具。", "version": "2.4.6", "id": "nhpbmiddokcokccmipfggcnmakeiggml", "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "action": {"default_icon": "assets/images/icon48.png", "default_popup": "popup.html", "default_title": "大卖家"}, "permissions": ["background", "contextMenus", "tabs", "notifications", "activeTab", "declarativeNetRequest", "unlimitedStorage", "storage"], "host_permissions": ["https://img.linssing.com/*", "https://img.damaijia.cn/*", "https://www.damaijia.cn/*", "https://img.damaio.com/*", "https://www.damaio.com/*"], "background": {"service_worker": "js/service-worker.js"}, "icons": {"16": "assets/images/icon16.png", "48": "assets/images/icon48.png", "128": "assets/images/icon128.png"}, "content_scripts": [{"matches": ["<all_urls>"], "css": [], "js": ["js/chunk-vendors.js", "js/chunk-common.js", "js/content.js"], "run_at": "document_end"}], "web_accessible_resources": [{"resources": ["js/inject.js", "js/tablestore-js-sdk-4.0.9.min.js", "js/jszip.min.js"], "matches": ["<all_urls>"], "use_dynamic_url": true}], "externally_connectable": {"matches": ["https://*.damaijia.cn/*", "https://*.damaio.com/*", "http://localhost:3000/*"], "web_accessible_resources": ["<all_urls>"]}, "chrome_url_overrides": {"newtab": "nav.html"}}