<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e6ddeba9-6d6c-48d1-9747-afbf247a69ed" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zXPXq6nYjRxWND5b7LgPgu0VYi" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.bit_api.executor&quot;: &quot;Run&quot;,
    &quot;Python.ceshi.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/2025/7月/豆包图片生成7.12&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.scopes&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\2025\7月\比特TEMU采集" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e6ddeba9-6d6c-48d1-9747-afbf247a69ed" name="更改" comment="" />
      <created>1751874487114</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751874487114</updated>
      <workItem from="1751874488162" duration="1171000" />
      <workItem from="1751883425181" duration="1631000" />
      <workItem from="1751956164124" duration="54000" />
      <workItem from="1751970935087" duration="10000" />
      <workItem from="1751970956976" duration="1395000" />
      <workItem from="1752052000306" duration="1969000" />
      <workItem from="1752056758642" duration="2189000" />
      <workItem from="1752064044742" duration="5601000" />
      <workItem from="1752073760603" duration="2208000" />
      <workItem from="1752076565272" duration="94000" />
      <workItem from="1752110644434" duration="6000" />
      <workItem from="1752116790977" duration="728000" />
      <workItem from="1752120502274" duration="2475000" />
      <workItem from="1752123579500" duration="1407000" />
      <workItem from="1752126356704" duration="14642000" />
      <workItem from="1752146056741" duration="8220000" />
      <workItem from="1752198289683" duration="3617000" />
      <workItem from="1752206163857" duration="5881000" />
      <workItem from="1752213132276" duration="6937000" />
      <workItem from="1752224880311" duration="236000" />
      <workItem from="1752225693385" duration="5966000" />
      <workItem from="1752250463528" duration="180000" />
      <workItem from="1752295391294" duration="970000" />
      <workItem from="1752323041431" duration="694000" />
      <workItem from="1752326789663" duration="717000" />
      <workItem from="1752377977913" duration="852000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/TEMU$main.coverage" NAME="main 覆盖结果" MODIFIED="1752140110850" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/TEMU$bit_api.coverage" NAME="bit_api 覆盖结果" MODIFIED="1752147560261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/TEMU$ceshi.coverage" NAME="ceshi 覆盖结果" MODIFIED="1752326797372" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>