import requests
from DrissionPage import ChromiumPage
import json
import time
import re
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading


class 浏览器:
    def __init__(self, id=None, 界面回调=None):
        self.卖家中心 = None
        self.大卖家 = None
        self.亚马逊 = None
        self.上品 = False
        self.核价 = False
        self.活动 = False
        self.跟价 = False
        self.上品数量 = 200
        self.最低价 = 20
        self.最高价 = 70
        self.关键词文本 = f"Triathlon\nSleeveless Suits\nFull Suits\nPants\nTops\nShorty Suits\nCaps"
        self.关键词 = self.关键词文本.split("\n")  # 将关键词文本转换为列表
        self.界面回调 = 界面回调  # 用于更新UI的回调函数
        self.运行中 = False  # 控制任务运行状态

        self.浏览器对象 = self.打开浏览器(id)
        self.记录日志("启动完成")
        self.当前标签页 = self.浏览器对象.latest_tab
        self.初始化所有标签页()

    def 记录日志(self, 消息):
        """记录日志并更新UI"""
        print(消息)
        if self.界面回调:
            self.界面回调(消息)
    def 执行搜索(self):
        self.记录日志("开始搜索...")
        输入框 = self.亚马逊.ele('x://input[@id="twotabsearchtextbox"]')

        if 输入框:
            输入框.clear()  # 清除输入框内容
            输入框.input(self.关键词[1])  # 输入关键词
            self.亚马逊.ele('x://input[@id="nav-search-submit-button"]',timeout=60).click()  # 点击搜索按钮

            self.亚马逊.ele('x://span[@class="a-button-text a-declarative"]',timeout=60).click()  # 点击"排序方式"下拉菜单

            self.亚马逊.ele(
                'x://a[@class="a-dropdown-link" and text()="Best Sellers"]',timeout=60).click()  # 点击"按相关性排序"下拉菜单中的"价格：从低到高"选项
            time.sleep(3)  # 等待页面加载

        当前网址 = self.亚马逊.url
        self.亚马逊.get(f"{当前网址}&low-price={self.最低价}&high-price={self.最高价}")  # 重新加载页面以应用价格过滤

        if self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input'):
            self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input',timeout=60).click()  # 如果"亚马逊免费送货"选项未选中，则点击它

        self.记录日志(f"搜索完成，当前URL: {self.亚马逊.url}")
    def 启动批量上品(self):
        self.记录日志("启动批量上品...")
        if self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]'):  # 暂停上品按钮存在
            self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]').click()

        self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="批量上品"]').click()  # 点击批量上品按钮
        self.亚马逊.ele('x:(//div[@class="ant-modal-content"]//span[@class="ant-radio-inner"])[1]').click()  # 点击是
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').clear()  # 清除输入框内容
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').input(str(self.上品数量))  # 输入数量
        self.亚马逊.ele('x://div[@class="ant-modal-content"]//span[text()="确 定"]').click()  # 点击确定
    def 检查批量上品状态(self):
        self.记录日志('检查批量上品是否正常运行')
        开始时间 = time.time()
        while True:
            if not self.运行中:  # 如果用户停止了任务
                return False
            if time.time() - 开始时间 > 180:  # 超过180秒则退出循环
                return False
            if len(self.亚马逊.eles('x://div[@class="ant-message-notice-content"]')) > 0:
                self.记录日志('批量上品正常运行中......')
                return True
            time.sleep(1)
    def 初始化所有标签页(self):
        所有标签页ID = self.浏览器对象.tab_ids
        self.记录日志(f"标签页ID: {所有标签页ID}")
        for 标签页ID in 所有标签页ID:
            标签页 = self.浏览器对象.get_tab(标签页ID)
            self.记录日志(f"标签页ID: {标签页ID}, 名称: {标签页.title}")
            if 标签页.title == "卖家中心" and self.卖家中心 is None:
                self.卖家中心 = 标签页
            elif 标签页.title == "大卖家" and self.大卖家 is None:
                self.大卖家 = 标签页
            elif re.match(r"^Amazon\.com", 标签页.title) and self.亚马逊 is None:
                self.亚马逊 = 标签页
            else:
                标签页.close()
        self.检查卖家中心状态()
        self.检查亚马逊状态()
        self.检查大卖家状态()
    def 检查卖家中心状态(self):
        if self.卖家中心 is None:
            self.卖家中心 = self.浏览器对象.new_tab(
                url="https://seller.kuajingmaihuo.com/login")  # 如果卖家中心标签未找到，则创建一个新的标签页
            time.sleep(2)

        if self.卖家中心.ele('x://span[text()="确认缴费"]', timeout=3):  # 检查是否有确认缴费按钮
            return True

        if self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]'):  # 检查是否有登录按钮
            self.卖家中心.ele('x://div[@data-testid="beast-core-checkbox-checkIcon"]').click()  # 勾选同意
            time.sleep(1)
            self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]').click()
    def 检查大卖家状态(self):
        if self.大卖家 is None:
            self.大卖家 = self.浏览器对象.new_tab(
                url="chrome-extension://olngdolageopicdnifleobajagpljpdc/nav.html")  # 如果大卖家标签未找到，则创建一个新的标签页
            time.sleep(2)

        while self.大卖家.ele(
                'x://div[@class="ant-space-item"]/button/span[text()="再次重试"]') and not self.大卖家.ele(
                'x://button[@class="css-1p3hq3p ant-btn ant-btn-default"]/span[text()="自动核价"]'):
            self.大卖家.ele('x://div[@class="ant-space-item"]/button/span[text()="再次重试"]').click()
            time.sleep(1)
    def 检查亚马逊状态(self):
        if self.亚马逊 is None:
            self.亚马逊 = self.浏览器对象.new_tab(url="https://www.amazon.com/")  # 如果亚马逊标签未找到，则创建一个新的标签页
    def 打开浏览器(self, id):  # 直接指定ID打开窗口，也可以使用 创建浏览器 方法返回的ID
        请求数据 = {"id": f'{id}'}
        响应 = requests.post("http://127.0.0.1:54345/browser/open", data=json.dumps(请求数据),
                             headers={'Content-Type': 'application/json'}).json()
        http地址 = 响应['data']['http']
        self.记录日志(http地址)
        浏览器实例 = ChromiumPage(addr_or_opts=http地址)
        return 浏览器实例  # 返回一个浏览器对象

    def 创建浏览器(self):  # 创建或者更新窗口，指纹参数 browserFingerPrint 如没有特定需求，只需要指定下内核即可，如果需要更详细的参数，请参考文档
        请求数据 = {
            'name': 'google',  # 窗口名称
            'remark': '',  # 备注
            'proxyMethod': 2,  # 代理方式 2自定义 3 提取IP
            # 代理类型  ['noproxy', 'http', 'https', 'socks5', 'ssh']
            'proxyType': 'socks5',
            'host': '************',  # 代理主机
            'port': '20901',  # 代理端口
            'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
            "proxyPassword": '02695401',  # 代理密码
            "browserFingerPrint": {  # 指纹对象
                'coreVersion': '124'  # 内核版本，注意，win7/win8/winserver 2012 已经不支持112及以上内核了，无法打开
            }
        }

        响应 = requests.post(f"{self.url}/browser/update",
                             data=json.dumps(请求数据), headers=self.headers).json()
        浏览器ID = 响应['data']['id']
        self.记录日志(浏览器ID)
        return 浏览器ID

    def 更新浏览器(self,id):  # 更新窗口，支持批量更新和按需更新，ids 传入数组，单独更新只传一个id即可，只传入需要修改的字段即可，比如修改备注，具体字段请参考文档，browserFingerPrint指纹对象不修改，则无需传入
        请求数据 = {'ids': id,  #
                    'remark': '我是一个备注',
                    'proxyType': 'socks5',
                    'host': '*************',  # 代理主机
                    'port': '30641',  # 代理端口
                    'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
                    "proxyPassword": '02695401',  # 代理密码
                    "browserFingerPrint": {
                        "coreVersion": "130",
                        "ostype": "PC",
                        "os": "Win32",
                        "osVersion": "11,10"
                    },
                    }
        响应 = requests.post(f"{self.url}/browser/update/partial",
                             data=json.dumps(请求数据), headers=self.headers).json()
        self.记录日志(响应)

    def 关闭浏览器(self, id):  # 关闭窗口
        请求数据 = {'id': f'{id}'}
        requests.post(f"{self.url}/browser/close",
                      data=json.dumps(请求数据), headers=self.headers).json()

    def 删除浏览器(self, id):  # 删除窗口
        请求数据 = {'id': f'{id}'}
        self.记录日志(requests.post(f"{self.url}/browser/delete",
                                    data=json.dumps(请求数据), headers=self.headers).json())
    def 执行任务(self):
        """执行所有选中的任务"""
        try:
            self.运行中 = True
            if self.上品:
                self.执行搜索()
                self.启动批量上品()
                if not self.检查批量上品状态():
                    self.记录日志("批量上品检查失败")
            if self.核价:
                self.记录日志("执行核价任务...")
                # 这里添加核价任务代码
            if self.活动:
                self.记录日志("执行活动任务...")
                # 这里添加活动任务代码
            if self.跟价:
                self.记录日志("执行跟价任务...")
                # 这里添加跟价任务代码
        except Exception as 异常:
            self.记录日志(f"发生错误: {str(异常)}")
        finally:
            self.运行中 = False
class 浏览器应用:
    def __init__(self, 根窗口):
        self.根窗口 = 根窗口
        self.根窗口.title("亚马逊自动采集")
        self.浏览器 = None
        self.运行中 = False

        # 设置窗口大小
        self.根窗口.geometry("400x600")

        # 创建主框架
        self.主框架 = ttk.Frame(根窗口, padding="10")
        self.主框架.pack(fill=tk.BOTH, expand=True)

        # 任务选择区域
        self.创建任务框架()

        # 参数设置区域
        self.创建参数框架()

        # 关键词输入区域
        self.创建关键词框架()

        # 日志输出区域
        self.创建日志框架()

        # 控制按钮区域
        self.创建控制框架()

    def 创建任务框架(self):
        """创建任务选择区域"""
        任务框架 = ttk.LabelFrame(self.主框架, text="任务选择", padding="10")
        任务框架.pack(fill=tk.X, pady=5)

        self.上品变量 = tk.BooleanVar()
        self.核价变量 = tk.BooleanVar()
        self.活动变量 = tk.BooleanVar()
        self.跟价变量 = tk.BooleanVar()

        ttk.Checkbutton(任务框架, text="上品", variable=self.上品变量).grid(row=0, column=0, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="核价", variable=self.核价变量).grid(row=0, column=1, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="活动", variable=self.活动变量).grid(row=0, column=2, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="跟价", variable=self.跟价变量).grid(row=0, column=3, padx=5, sticky=tk.W)

    def 创建参数框架(self):
        """创建参数设置区域"""
        参数框架 = ttk.LabelFrame(self.主框架, text="参数设置", padding="10")
        参数框架.pack(fill=tk.X, pady=5)

        # 上品数量
        ttk.Label(参数框架, text="上品数量:").grid(row=0, column=0, padx=5, sticky=tk.W)
        self.上品数量输入框 = ttk.Entry(参数框架, width=6)
        self.上品数量输入框.grid(row=0, column=1, padx=5, sticky=tk.W)
        self.上品数量输入框.insert(0, "200")

        # 最低价
        ttk.Label(参数框架, text="最低价:").grid(row=0, column=2, padx=5, sticky=tk.W)
        self.最低价输入框 = ttk.Entry(参数框架, width=6)
        self.最低价输入框.grid(row=0, column=3, padx=5, sticky=tk.W)
        self.最低价输入框.insert(0, "20")

        # 最高价
        ttk.Label(参数框架, text="最高价:").grid(row=0, column=4, padx=5, sticky=tk.W)
        self.最高价输入框 = ttk.Entry(参数框架, width=6)
        self.最高价输入框.grid(row=0, column=5, padx=5, sticky=tk.W)
        self.最高价输入框.insert(0, "70")

    def 创建关键词框架(self):
        """创建关键词输入区域"""
        关键词框架 = ttk.LabelFrame(self.主框架, text="关键词", padding="10")
        关键词框架.pack(fill=tk.BOTH, expand=True, pady=5)

        self.关键词文本框 = scrolledtext.ScrolledText(关键词框架, height=8)
        self.关键词文本框.pack(fill=tk.BOTH, expand=True)
        self.关键词文本框.insert(tk.END, "Triathlon\nSleeveless Suits\nFull Suits\nPants\nTops\nShorty Suits\nCaps")

    def 创建日志框架(self):
        """创建日志输出区域"""
        日志框架 = ttk.LabelFrame(self.主框架, text="日志输出", padding="10")
        日志框架.pack(fill=tk.BOTH, expand=True, pady=5)

        self.日志文本框 = scrolledtext.ScrolledText(日志框架, height=10)
        self.日志文本框.pack(fill=tk.BOTH, expand=True)

    def 创建控制框架(self):
        """创建控制按钮区域"""
        控制框架 = ttk.Frame(self.主框架)
        控制框架.pack(fill=tk.X, pady=5)

        self.启动按钮 = ttk.Button(控制框架, text="启动", command=self.切换运行状态)
        self.启动按钮.pack(pady=5)

    def 记录日志(self, 消息):
        """记录日志到界面"""
        self.日志文本框.insert(tk.END, f"{消息}\n")
        self.日志文本框.see(tk.END)
        self.根窗口.update_idletasks()

    def 切换运行状态(self):
        """启动/停止任务"""
        if not self.运行中:
            # 获取界面参数
            上品选中 = self.上品变量.get()
            核价选中 = self.核价变量.get()
            活动选中 = self.活动变量.get()
            跟价选中 = self.跟价变量.get()

            if not any([上品选中, 核价选中, 活动选中, 跟价选中]):
                messagebox.showwarning("警告", "请至少选择一个任务!")
                return

            try:
                上品数量 = int(self.上品数量输入框.get())
                最低价 = int(self.最低价输入框.get())
                最高价 = int(self.最高价输入框.get())
                关键词 = self.关键词文本框.get("1.0", tk.END).strip()

                if 上品数量 <= 0:
                    messagebox.showwarning("警告", "上品数量必须大于0!")
                    return

                if 最低价 >= 最高价:
                    messagebox.showwarning("警告", "最低价必须小于最高价!")
                    return

                if not 关键词:
                    messagebox.showwarning("警告", "请输入关键词!")
                    return

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字!")
                return

            # 创建浏览器实例
            self.浏览器 = 浏览器('2969a7a195054eaa92f5a70efbeb623b', self.记录日志)

            # 设置参数
            self.浏览器.上品 = 上品选中
            self.浏览器.核价 = 核价选中
            self.浏览器.活动 = 活动选中
            self.浏览器.跟价 = 跟价选中
            self.浏览器.上品数量 = 上品数量
            self.浏览器.最低价 = 最低价
            self.浏览器.最高价 = 最高价
            self.浏览器.关键词文本 = 关键词
            self.浏览器.关键词 = 关键词.split("\n")

            # 启动任务线程
            self.运行中 = True
            self.启动按钮.config(text="停止")
            threading.Thread(target=self.执行所有任务, daemon=True).start()
        else:
            self.运行中 = False
            if self.浏览器:
                self.浏览器.运行中 = False
            self.启动按钮.config(text="启动")

    def 执行所有任务(self):
        """执行任务"""
        try:
            self.浏览器.执行任务()
        except Exception as 异常:
            self.记录日志(f"发生错误: {str(异常)}")
        finally:
            self.运行中 = False
            self.启动按钮.config(text="启动")

if __name__ == "__main__":
    根窗口 = tk.Tk()
    应用 = 浏览器应用(根窗口)
    根窗口.mainloop()