import requests
from DrissionPage import ChromiumPage
import json
import time
import re
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import os


class 浏览器:
    def __init__(self, id=None, 界面回调=None):
        self.卖家中心 = None
        self.大卖家 = None
        self.亚马逊 = None
        self.上品 = False
        self.核价 = False
        self.活动 = False
        self.跟价 = False
        self.活动内容 = ["限时秒杀", "官方大促"]  # 活动内容列表
        self.上品数量 = 200
        self.最低价 = 20
        self.最高价 = 70
        self.当前任务 = None  # 当前任务
        self.关键词 = []
        self.界面回调 = 界面回调  # 用于更新UI的回调函数
        self.运行中 = False  # 控制任务运行状态
        self.id = id  # 存储浏览器ID
        self.浏览器对象 = self.打开浏览器(id)
        self.当前标签页 = self.浏览器对象.latest_tab
        self.初始化所有标签页()
    def 记录日志(self, 消息):
        """记录日志并更新UI"""
        print(消息)
        if self.界面回调:
            self.界面回调(消息)
    def 初始化所有标签页(self):
        所有标签页ID = self.浏览器对象.tab_ids
        self.记录日志(f"标签页ID: {所有标签页ID}")
        for 标签页ID in 所有标签页ID:
            标签页 = self.浏览器对象.get_tab(标签页ID)
            self.记录日志(f"标签页ID: {标签页ID}, 名称: {标签页.title}")
            if 标签页.title == "卖家中心" and self.卖家中心 is None:
                self.卖家中心 = 标签页
            elif 标签页.title == "大卖家" and self.大卖家 is None:
                self.大卖家 = 标签页
            elif re.match(r"^Amazon\.com", 标签页.title) and self.亚马逊 is None:
                self.亚马逊 = 标签页
            else:
                标签页.close()
        self.检查卖家中心状态()
        self.检查亚马逊状态()
        self.检查大卖家状态()
    def 检查卖家中心状态(self):
        if self.卖家中心 is None:
            self.卖家中心 = self.浏览器对象.new_tab(
                url="https://seller.kuajingmaihuo.com/login")  # 如果卖家中心标签未找到，则创建一个新的标签页
            time.sleep(2)

        if self.卖家中心.ele('x://span[text()="确认缴费"]', timeout=3):  # 检查是否有确认缴费按钮
            return True

        if self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]'):  # 检查是否有登录按钮
            self.卖家中心.ele('x://div[@data-testid="beast-core-checkbox-checkIcon"]').click()  # 勾选同意
            time.sleep(1)
            self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]').click()
    def 检查大卖家状态(self):
        if self.大卖家 is None:
            self.大卖家 = self.浏览器对象.new_tab(
                url="chrome-extension://nhpbmiddokcokccmipfggcnmakeiggml/nav.html")  # 如果大卖家标签未找到，则创建一个新的标签页
            time.sleep(2)

        while self.大卖家.ele(
                'x://div[@class="ant-space-item"]/button/span[text()="再次重试"]') and not self.大卖家.ele(
                'x://button[@class="css-1p3hq3p ant-btn ant-btn-default"]/span[text()="自动核价"]'):
            self.大卖家.ele('x://div[@class="ant-space-item"]/button/span[text()="再次重试"]').click()
            time.sleep(1)
    def 检查亚马逊状态(self):
        if self.亚马逊 is None:
            self.亚马逊 = self.浏览器对象.new_tab(url="https://www.amazon.com/")  # 如果亚马逊标签未找到，则创建一个新的标签页
    def 执行搜索(self, 关键词):

        self.记录日志("开始搜索...")
        输入框 = self.亚马逊.ele('x://input[@id="twotabsearchtextbox"]', timeout=30)

        if 输入框:
            输入框.clear()  # 清除输入框内容
            输入框.input(关键词)  # 输入关键词
            self.亚马逊.ele('x://input[@id="nav-search-submit-button"]').click()  # 点击搜索按钮

            self.亚马逊.ele('x://span[@class="a-button-text a-declarative"]', timeout=30).click()  # 点击"排序方式"下拉菜单

            self.亚马逊.ele(
                'x://a[@class="a-dropdown-link" and text()="Best Sellers"]',
                timeout=30).click()  # 点击"按相关性排序"下拉菜单中的"价格：从低到高"选项
            time.sleep(3)  # 等待页面加载

        当前网址 = self.亚马逊.url
        self.亚马逊.get(f"{当前网址}&low-price={self.最低价}&high-price={self.最高价}")  # 重新加载页面以应用价格过滤

        if self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input'):
            self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input',
                            timeout=30).click()  # 如果"亚马逊免费送货"选项未选中，则点击它

        self.记录日志(f"搜索完成，当前URL: {self.亚马逊.url}")
    def 启动上品(self):

        if self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]'):  # 暂停上品按钮存在
            self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]').click()
        self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="批量上品"]').click()  # 点击批量上品按钮
        self.亚马逊.ele('x:(//div[@class="ant-modal-content"]//span[@class="ant-radio-inner"])[1]').click()  # 点击是
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').clear()  # 清除输入框内容
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').input(str(self.上品数量))  # 输入数量
        self.亚马逊.ele('x://div[@class="ant-modal-content"]//span[text()="确 定"]').click()  # 点击确定
        self.记录日志("上品已启动")
    def 上品s(self):

        self.执行搜索(self.关键词[0])  # 使用第一个关键词进行搜索
        self.启动上品()
    def 检查上品(self):
        self.记录日志('检查批量上品是否正常运行')
        开始时间 = time.time()
        while True:
            if not self.运行中:  # 如果用户停止了任务
                return False
            if time.time() - 开始时间 > 120:  # 超过120秒则退出循环
                self.记录日志('批量上品检查超时，重新启动上品')
                self.启动上品()
                return False
            if len(self.亚马逊.eles('x://div[@class="ant-message-notice-content"]')) > 0:
                self.记录日志('批量上品正常运行中......')
                return True

            运行文本 = self.亚马逊.ele('x://sup[@class="ant-scroll-number ant-badge-count ant-badge-multiple-words"]').text
            已上传数量 = int(re.search(r"(.*)成功", 运行文本))

            if 已上传数量 >= self.上品数量:
                self.记录日志(f"已成功上品 {已上传数量} 件商品")

                if len(self.关键词) > 0:
                    self.执行搜索(self.关键词.pop(0))  # 使用下一个关键词进行搜索
                    self.启动上品()  # 重新启动上品
                else:
                    self.记录日志("所有关键词已处理完毕")
                    self.上品 = False  # 设置上品状态为False

            time.sleep(1)
    def 核价s(self):

        """执行核价操作"""
        self.当前任务 = "核价"  # 设置当前任务为核价
        try:
            if self.大卖家 is None:
                self.记录日志("大卖家标签未找到，无法执行核价")
                return


            if self.大卖家.ele('x://div[@class="ant-space-item"]//span[text()="暂停核价"]',timeout=3):  # 暂停核价按钮存在
                self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动报名活动"]').click()
                time.sleep(1)
                self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动核价成本"]').click()  # 点击自动核价成本
                time.sleep(1)


            if self.大卖家.ele('x://button[@class="css-1p3hq3p ant-btn ant-btn-default"]/span[text()="自动核价"]'):
                self.大卖家.ele('x://button[@class="css-1p3hq3p ant-btn ant-btn-default"]/span[text()="自动核价"]').click()
                time.sleep(2)
                self.大卖家.ele('x://div[@class="ant-space-item"]//span[text()="开始自动核价"]').click()  # 点击开始自动核价按钮
                time.sleep(2)
                self.大卖家.ele('x://div[@class="ant-modal-content"]//span[text()="确 定"]').click() # 点击确定按钮

                time.sleep(5)  # 等待核价操作完成

                # 等待核价结果加载
                self.大卖家.wait.ele_displayed('x://span[@class="ant-progress-text"]/span', timeout=60)

                print("核价已启动")
                self.记录日志("核价已启动")
            else:
                self.记录日志("核价按钮未找到，无法执行核价")

        except Exception as e:
            self.记录日志(f"执行核价时发生错误: {str(e)}")
            return False
    def 检查核价(self):

        try:
            if self.大卖家.ele('x://span[text()="当前店铺已处理完成"]'):
                self.记录日志("核价已完成")
                self.核价 = False  # 设置核价状态为False
                return True

            elif self.大卖家.ele('x://span[text()="正在自动核价中，请勿关闭页面"]'):
                self.记录日志("核价正在进行中...")

                start_time = time.time()
                while True:
                    if not self.运行中:
                        self.记录日志("任务已停止")
                        return False

                    if self.大卖家.ele('x://div[@class="ant-message-notice-content"]'):  # 如果有提示信息弹窗
                        return False

                    if time.time() - start_time > 120:
                        self.记录日志("核价停滞超时，重新启动核价")
                        self.核价s()
                        return False
                    time.sleep(1)  # 每1秒检查一次

        except Exception as e:
            self.记录日志(f"检查核价状态时发生错误: {str(e)}")
            return False

    def 活动s(self,活动名称):

        """执行活动报名操作"""
        self.当前任务 = "活动"  # 设置当前任务为活动

        while not self.大卖家.ele('x://div[@class="display-align-items"]//div[@class="ant-form-item-control-input-content"]//div[@class="ant-select-selector"]/span[text()="限时秒杀" or text()="官方大促"]',timeout=3):

            self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动核价成本"]').click()  # 点击自动核价成本
            time.sleep(0.5)
            self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动报名活动"]').click()  # 点击自动报名活动
            time.sleep(2)


        if self.大卖家.ele('x://div[@class="display-align-items"]//div[@class="ant-select-selector"]',timeout=10).text != 活动名称:

            self.大卖家.ele('x://div[@class="display-align-items"]//div[@class="ant-select-selector"]').click()  # 点击活动选择框
            time.sleep(1)
            self.大卖家.ele(f'x://div[@class="ant-select-item-option-content" and text()="{活动名称}"]').click()  # 选择活动
            time.sleep(1)


        while True:
            self.大卖家.ele('x://div[@class="ant-spin-container"]//span[text()="开始报活动"]').click()  # 点击开始报活动按钮
            time.sleep(0.5)
            if self.大卖家.ele('x:(//div[@class="ant-message-custom-content ant-message-error"]/span[text()="登录过期，请重新登录"])[1]', timeout=3):
                self.记录日志("登录过期重新登录")
                self.检查卖家中心状态()
                time.sleep(3)
            elif self.大卖家.ele('x://div[@class="ant-drawer-content"]//span[text()="暂停报活动"]', timeout=3):
                break
        self.记录日志(f"已启动活动: {活动名称}")

    def 检查活动(self):
        """检查活动报名状态"""
        try:
            if self.大卖家.ele('x://span[text()="当前店铺已处理完成"]'):
                self.记录日志("活动报名已完成")


                已完成活动 = self.大卖家.ele('x://div[@class="display-align-items"]//div[@class="ant-space-item"]//span[@class="ant-select-selection-item"]', timeout=3).text

                #从self.活动内容中移除已完成的活动
                if 已完成活动 in self.活动内容:
                    self.活动内容.remove(已完成活动)
                    self.记录日志(f"已完成活动: {已完成活动}")
                return True

            elif self.大卖家.ele('x://span[text()="正在自动报名中，请勿关闭页面"]'):
                self.记录日志("活动报名正在进行中...")

                start_time = time.time()
                while True:
                    if not self.运行中:
                        self.记录日志("任务已停止")
                        return False

                    if self.大卖家.ele('x://div[@class="ant-message-notice-content"]'):  # 如果有提示信息弹窗
                        return False

                    if time.time() - start_time > 120:
                        self.记录日志("活动报名停滞超时，重新启动活动报名")
                        self.活动(self.活动[0])  # 重新启动第一个活动
                        return False
                    time.sleep(1)  # 每1秒检查一次

        except Exception as e:
            self.记录日志(f"检查活动状态时发生错误: {str(e)}")
            return False

    def 跟价s(self):

        """执行跟价操作"""

        self.当前任务 = "跟价"  # 设置当前任务为跟价

        if self.大卖家 is None:
            self.记录日志("大卖家标签未找到，无法执行跟价")
            return

        while not self.大卖家.ele('x://span[text()="开始调价"]', timeout=3):
            self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动核价成本"]').click()
            time.sleep(0.5)
            self.大卖家.ele('x://span[@class="ant-menu-title-content" and text()="自动跟价管理"]').click()  # 循环判断 确保跟价按钮存在


        while True:

            self.大卖家.ele('x://span[text()="开始调价"]').click()  # 点击开始调价按钮
            time.sleep(0.5)

            if self.大卖家.ele('x:(//div[@class="ant-message-custom-content ant-message-error"]/span[text()="登录过期，请重新登录"])[1]', timeout=3):
                self.记录日志("登录过期重新登录")
                self.检查卖家中心状态()
                time.sleep(3)

            elif self.大卖家.ele('x:(//div[@class="ant-message-custom-content ant-message-error"]/span[text()="当前店铺暂无可处理的数据"])[1]', timeout=3):
                self.记录日志("当前店铺暂无可处理的数据")
                self.跟价 = False  # 设置跟价状态为False
                return True

            elif not self.大卖家.ele('x://span[text()="开始调价"]', timeout=3):
                break

    def 检查跟价(self):

        """检查跟价状态"""
        try:
            if self.大卖家.ele('x://span[text()="当前店铺已处理完成"]'):
                self.记录日志("跟价已完成")
                return True

            elif self.大卖家.ele('x://span[text()="正在自动跟价中，请勿关闭页面"]'):
                self.记录日志("跟价正在进行中...")

                start_time = time.time()
                while True:
                    if not self.运行中:
                        self.记录日志("任务已停止")
                        return False

                    if self.大卖家.ele('x://div[@class="ant-message-notice-content"]'):  # 如果有提示信息弹窗
                        return False

                    if time.time() - start_time > 120:
                        self.记录日志("跟价停滞超时，重新启动跟价")
                        self.跟价()  # 重新启动跟价
                        return False
                    time.sleep(1)  # 每1秒检查一次

        except Exception as e:
            self.记录日志(f"检查跟价状态时发生错误: {str(e)}")
            return False

    #-----------------------------比特浏览器相关----------------------------
    def 打开浏览器(self, id):  # 直接指定ID打开窗口，也可以使用 创建浏览器 方法返回的ID
        请求数据 = {"id": f'{id}'}
        响应 = requests.post("http://127.0.0.1:54345/browser/open", data=json.dumps(请求数据),
                             headers={'Content-Type': 'application/json'}).json()
        http地址 = 响应['data']['http']
        self.记录日志(http地址)
        浏览器实例 = ChromiumPage(addr_or_opts=http地址)
        return 浏览器实例  # 返回一个浏览器对象

    def 创建浏览器(self):  # 创建或者更新窗口，指纹参数 browserFingerPrint 如没有特定需求，只需要指定下内核即可，如果需要更详细的参数，请参考文档
        请求数据 = {
            'name': 'google',  # 窗口名称
            'remark': '',  # 备注
            'proxyMethod': 2,  # 代理方式 2自定义 3 提取IP
            # 代理类型  ['noproxy', 'http', 'https', 'socks5', 'ssh']
            'proxyType': 'socks5',
            'host': '************',  # 代理主机
            'port': '20901',  # 代理端口
            'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
            "proxyPassword": '02695401',  # 代理密码
            "browserFingerPrint": {  # 指纹对象
                'coreVersion': '124'  # 内核版本，注意，win7/win8/winserver 2012 已经不支持112及以上内核了，无法打开
            }
        }

        响应 = requests.post(f"{self.url}/browser/update",
                             data=json.dumps(请求数据), headers=self.headers).json()
        浏览器ID = 响应['data']['id']
        self.记录日志(浏览器ID)
        return 浏览器ID

    def 更新浏览器(self,id):  # 更新窗口，支持批量更新和按需更新，ids 传入数组，单独更新只传一个id即可，只传入需要修改的字段即可，比如修改备注，具体字段请参考文档，browserFingerPrint指纹对象不修改，则无需传入
        请求数据 = {'ids': id,  #
                    'remark': '我是一个备注',
                    'proxyType': 'socks5',
                    'host': '*************',  # 代理主机
                    'port': '30641',  # 代理端口
                    'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
                    "proxyPassword": '02695401',  # 代理密码
                    "browserFingerPrint": {
                        "coreVersion": "130",
                        "ostype": "PC",
                        "os": "Win32",
                        "osVersion": "11,10"
                    },
                    }
        响应 = requests.post(f"{self.url}/browser/update/partial",
                             data=json.dumps(请求数据), headers=self.headers).json()
        self.记录日志(响应)

    def 关闭浏览器(self, id):  # 关闭窗口
        请求数据 = {'id': f'{id}'}
        requests.post(f"{self.url}/browser/close",
                      data=json.dumps(请求数据), headers=self.headers).json()

    def 删除浏览器(self, id):  # 删除窗口
        请求数据 = {'id': f'{id}'}
        self.记录日志(requests.post(f"{self.url}/browser/delete",
                                    data=json.dumps(请求数据), headers=self.headers).json())
    def 执行任务(self):



        """执行所有选中的任务"""
        #   ---------------------启动任务-----------------
        try:
            self.运行中 = True
            if self.上品:
                self.记录日志("启动上品任务...")
                self.上品s()
            if self.核价 and self.当前任务 == None:
                self.记录日志("启动核价任务...")
                self.核价s()
            if self.活动 and self.当前任务 == None:
                self.记录日志("启动活动任务...")
                self.活动s()
            if self.跟价 and self.当前任务 == None:
                self.记录日志("启动跟价任务...")
                self.跟价s()
        except Exception as 异常:
            self.记录日志(f"发生错误: {str(异常)}")


        #   ---------------------监控任务-----------------

        while self.运行中 and (self.上品 or self.核价 or self.活动 or self.跟价):
            if self.上品:
                self.检查上品()
            if self.当前任务 == "核价":
                self.检查核价()
            elif self.当前任务 == "活动":
                self.检查活动()
            elif self.当前任务 == "跟价":
                self.检查跟价()
            time.sleep(30)  # 每30秒检查一次任务状态
        self.记录日志("所有任务已完成或已停止")
class 浏览器应用:
    def __init__(self, 根窗口):
        self.根窗口 = 根窗口
        self.根窗口.title("亚马逊自动化工具")
        self.浏览器 = None
        self.运行中 = False
        self.配置数据 = self.读取配置文件()
        self.当前选中配置 = None

        # 设置窗口大小
        self.根窗口.geometry("330x500")

        # 创建主框架
        self.主框架 = ttk.Frame(根窗口, padding="10")
        self.主框架.pack(fill=tk.BOTH, expand=True)

        """创建任务选择区域（现在包含启动按钮）"""
        任务框架 = ttk.LabelFrame(self.主框架, text="任务选择", padding="10")
        任务框架.pack(fill=tk.X, pady=5)

        self.上品变量 = tk.BooleanVar()
        self.核价变量 = tk.BooleanVar()
        self.活动变量 = tk.BooleanVar()
        self.跟价变量 = tk.BooleanVar()

        # 任务选择复选框
        ttk.Checkbutton(任务框架, text="上品", variable=self.上品变量).grid(row=0, column=0, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="核价", variable=self.核价变量).grid(row=0, column=1, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="活动", variable=self.活动变量).grid(row=0, column=2, padx=5, sticky=tk.W)
        ttk.Checkbutton(任务框架, text="跟价", variable=self.跟价变量).grid(row=0, column=3, padx=5, sticky=tk.W)

        # 将启动按钮放在任务框架的最右边
        self.启动按钮 = ttk.Button(任务框架, text="启动", command=self.切换运行状态)
        self.启动按钮.grid(row=0, column=4, padx=5, sticky=tk.E)

        # 配置列权重，让启动按钮靠右
        任务框架.columnconfigure(4, weight=1)

        # 参数设置区域
        self.创建参数框架()
    def 创建参数框架(self):
        """创建参数设置区域"""
        参数框架 = ttk.LabelFrame(self.主框架, text="参数设置", padding="10")
        参数框架.pack(fill=tk.X, pady=5)

        # 左侧参数区域
        左侧框架 = ttk.Frame(参数框架)
        左侧框架.grid(row=0, column=0, sticky=tk.W)

        # 上品数量
        ttk.Label(左侧框架, text="数量:").grid(row=0, column=0, padx=2, sticky=tk.W)
        self.上品数量输入框 = ttk.Entry(左侧框架, width=12)
        self.上品数量输入框.grid(row=0, column=1, padx=3, sticky=tk.W)
        self.上品数量输入框.insert(0, "200")

        # 最低价
        ttk.Label(左侧框架, text="最低价:").grid(row=1, column=0, padx=2, sticky=tk.W)
        self.最低价输入框 = ttk.Entry(左侧框架, width=12)
        self.最低价输入框.grid(row=1, column=1, padx=3, sticky=tk.W)
        self.最低价输入框.insert(0, "20")

        # 最高价
        ttk.Label(左侧框架, text="最高价:").grid(row=2, column=0, padx=2, sticky=tk.W)
        self.最高价输入框 = ttk.Entry(左侧框架, width=12)
        self.最高价输入框.grid(row=2, column=1, padx=3, sticky=tk.W)
        self.最高价输入框.insert(0, "70")

        # 关键词
        ttk.Label(左侧框架, text="关键词:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.关键词输入框 = scrolledtext.ScrolledText(左侧框架, width=20, height=7)
        self.关键词输入框.grid(row=4, column=0, columnspan=5, padx=5, pady=5, sticky=tk.W)
        self.关键词输入框.insert(tk.END, "Triathlon\nSleeveless Suits\nFull Suits\nPants\nTops\nShorty Suits\nCaps")

        # 右侧配置列表区域
        右侧框架 = ttk.Frame(参数框架)
        右侧框架.grid(row=0, column=1, padx=10, sticky=tk.E)

        ttk.Label(右侧框架, text="浏览器列表").pack(anchor=tk.W)
        self.配置列表框 = tk.Listbox(右侧框架, width=15, height=10)
        self.配置列表框.pack()

        """创建日志输出区域"""
        日志框架 = ttk.LabelFrame(self.主框架, text="日志输出", padding="10")
        日志框架.pack(fill=tk.BOTH, expand=True, pady=5)

        self.日志文本框 = scrolledtext.ScrolledText(日志框架, height=10)
        self.日志文本框.pack(fill=tk.BOTH, expand=True)


        # 填充浏览器列表
        for 序号 in self.配置数据:
            self.配置列表框.insert(tk.END, f"浏览器{序号}")

        # 绑定选择事件
        self.配置列表框.bind('<<ListboxSelect>>', self.选择配置)
    def 读取配置文件(self):
        """读取config.json配置文件"""
        配置文件路径 = os.path.join(os.path.dirname(__file__), 'config.json')
        try:
            with open(配置文件路径, 'r', encoding='utf-8') as 文件:
                return json.load(文件)
        except FileNotFoundError:
            messagebox.showerror("错误", "未找到config.json配置文件")
            return {}
        except json.JSONDecodeError:
            messagebox.showerror("错误", "config.json文件格式错误")
            return {}
    def 选择配置(self, event):
        """处理配置列表选择事件"""
        选中项 = self.配置列表框.curselection()
        if 选中项:
            序号 = str(选中项[0] + 1)  # 转换为1-based序号
            self.当前选中配置 = self.配置数据.get(序号)
            self.记录日志(f"浏览器 {序号}, ID: {self.当前选中配置}")
    def 记录日志(self, 消息):
        """记录日志到界面"""
        self.日志文本框.insert(tk.END, f"{消息}\n")
        self.日志文本框.see(tk.END)
        self.根窗口.update_idletasks()
    def 切换运行状态(self):
        """启动/停止任务"""
        if not self.运行中:
            # 检查是否选择了配置
            if not self.当前选中配置:
                messagebox.showwarning("警告", "请先选择一个配置!")
                return

            # 获取界面参数
            上品选中 = self.上品变量.get()
            核价选中 = self.核价变量.get()
            活动选中 = self.活动变量.get()
            跟价选中 = self.跟价变量.get()

            if not any([上品选中, 核价选中, 活动选中, 跟价选中]):
                messagebox.showwarning("警告", "请至少选择一个任务!")
                return

            try:
                上品数量 = int(self.上品数量输入框.get())
                最低价 = int(self.最低价输入框.get())
                最高价 = int(self.最高价输入框.get())
                关键词 = self.关键词输入框.get("1.0", tk.END).strip()

                if 上品数量 <= 0:
                    messagebox.showwarning("警告", "上品数量必须大于0!")
                    return

                if 最低价 >= 最高价:
                    messagebox.showwarning("警告", "最低价必须小于最高价!")
                    return

                if not 关键词:
                    messagebox.showwarning("警告", "请输入关键词!")
                    return

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字!")
                return

            # 创建浏览器实例
            self.浏览器 = 浏览器(self.当前选中配置, self.记录日志)

            # 设置参数
            self.浏览器.上品 = 上品选中
            self.浏览器.核价 = 核价选中
            self.浏览器.活动 = 活动选中
            self.浏览器.跟价 = 跟价选中
            self.浏览器.上品数量 = 上品数量
            self.浏览器.最低价 = 最低价
            self.浏览器.最高价 = 最高价
            self.浏览器.关键词文本 = 关键词
            self.浏览器.关键词 = 关键词.split("\n")

            # 启动任务线程
            self.运行中 = True
            self.启动按钮.config(text="停止")
            threading.Thread(target=self.执行所有任务, daemon=True).start()
        else:
            self.运行中 = False
            if self.浏览器:
                self.浏览器.运行中 = False
            self.启动按钮.config(text="启动")
    def 执行所有任务(self):
        """执行任务"""
        try:
            self.浏览器.执行任务()
        except Exception as 异常:
            self.记录日志(f"发生错误: {str(异常)}")
        finally:
            self.运行中 = False
            self.启动按钮.config(text="启动")

if __name__ == "__main__":
    根窗口 = tk.Tk()
    应用 = 浏览器应用(根窗口)
    根窗口.mainloop()