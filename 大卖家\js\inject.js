(location.href.includes("yosamall")||location.href.includes("gigab2b")||location.href.includes("westmonth")||location.href.includes("temu")||location.href.includes("temu")||location.href.includes("from=dmoLocal"))&&function(){try{var t=XMLHttpRequest.prototype,e=t.open,s=t.send;t.open=function(t,s){return this._method=t,this._url=s,e.apply(this,arguments)},t.send=function(t){var e=!1;if(this._url?.includes("api/localTemuProduct/add.json")&&t)for(let n of t.values())if(n instanceof File){let o=new JSZip;function i(){return new Promise((async t=>{const e=await o.loadAsync(n);let s={};for(let[i,n]of Object.entries(e.files))if(!n.dir){let t=await n.async("text");s[i]=t}for(let i in s)t(s[i]||"")}))}e=!0,i().then((t=>{window.dispatchEvent(new CustomEvent("popTemuProductAddJson",{detail:t}))}))}return this.addEventListener("load",(function(){if(window?.postMessage({cmd:"xhr",data:{result:this.response,url:this._url,postData:t}},"*"),this._url?.includes("api.ipartszone.com/distribution/selection/list")&&window.dispatchEvent(new CustomEvent("yosamallGoodsList",{detail:JSON.parse(this.response)})),this._url?.includes("api/v1/product/img/search")&&window.dispatchEvent(new CustomEvent("dobaGoodsList",{detail:JSON.parse(this.response)})),this._url?.includes("route=/product/list/list")&&window?.dispatchEvent(new CustomEvent("gigiGoodsList",{detail:JSON.parse(this.response)})),this._url?.includes("/shop_api/products/load_list")&&window?.dispatchEvent(new CustomEvent("westmonthGoodsList",{detail:JSON.parse(this.response)})),this._url?.includes("ms/bg-flux-ms/compliance_property/edit_compliance")&&window?.dispatchEvent(new CustomEvent("governComplianceTemplate",{detail:t})),this._url?.includes("api/flash/real_picture/pre_verification")){let e=JSON.parse(this.response);1==e.result.check_result&&window?.dispatchEvent(new CustomEvent("governLivePhotosTemplate",{detail:t}))}if(this._url?.includes("bg-lich-mms/cert/product/upload")){let e=JSON.parse(this.response);1==e.success&&window?.dispatchEvent(new CustomEvent("governQualificationsTemplate",{detail:t}))}})),!e&&s.apply(this,arguments)}}catch(i){}}(XMLHttpRequest),function(){if(location.href.includes("4supply.com")){const t=fetch;window.fetch=(e,s)=>t(e,s).then((async t=>{if(t.ok)return(location.href.includes("4supply.com/category")||location.href.includes("4supply.com/search"))&&e.includes("api/trantor/func/sitem_SitemListSearchListFunc")?t.json():t;throw new Error("请求失败")})).then((t=>{(location.href.includes("4supply.com/category")||location.href.includes("4supply.com/search"))&&e.includes("api/trantor/func/sitem_SitemListSearchListFunc")&&window.dispatchEvent(new CustomEvent("4supplyComGoodsList",{detail:t}))}))}}();