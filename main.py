import requests
from DrissionPage import ChromiumPage
import json
import time
import re

class 浏览器:
    def __init__(self, id=None):
        self.卖家中心 = None
        self.大卖家 = None
        self.亚马逊 = None
        self.上品 = False
        self.核价 = False
        self.活动 = False
        self.跟价 = False
        self.上品数量 = 200
        self.最低价 = 20
        self.最高价 = 70
        self.关键词文本 = f"Triathlon\nSleeveless Suits\nFull Suits\nPants\nTops\nShorty Suits\nCaps"
        self.关键词 = self.关键词文本.split("\n")  # 将关键词文本转换为列表

        self.llq = self.openBrowser(id)
        print("启动完成")
        self.tab = self.llq.latest_tab
        self.初始化所有标签()

    def 搜索(self):

        输入框 = self.亚马逊.ele('x://input[@id="twotabsearchtextbox"]')

        if 输入框:
            输入框.clear()  # 清除输入框内容
            输入框.input(self.关键词[1])  # 输入关键词
            self.亚马逊.ele('x://input[@id="nav-search-submit-button"]').click()  # 点击搜索按钮

            self.亚马逊.ele('x://span[@class="a-button-text a-declarative"]').click()  # 点击“排序方式”下拉菜单

            self.亚马逊.ele('x://a[@class="a-dropdown-link" and text()="Best Sellers"]').click()  # 点击“按相关性排序”下拉菜单中的“价格：从低到高”选项
            time.sleep(3)  # 等待页面加载


        url = self.亚马逊.url
        self.亚马逊.get(f"{url}&low-price={self.最低价}&high-price={self.最高价}")  # 重新加载页面以应用价格过滤


        if self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input'):
            self.亚马逊.ele('x://a[@aria-label="Apply Free Shipping by Amazon filter to narrow results"]//input').click()  # 如果“亚马逊免费送货”选项未选中，则点击它

        print("搜索完成，当前URL:", self.亚马逊.url)
    def 启动批量上品(self):

        if self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]'): #暂停上品按钮存在
            self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="暂停上品"]').click()

        self.亚马逊.ele('x://div[@class="ant-float-btn-description" and text()="批量上品"]').click()  # 点击批量上品按钮
        self.亚马逊.ele('x:(//div[@class="ant-modal-content"]//span[@class="ant-radio-inner"])[1]').click()  # 点击是
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').clear()  # 清除输入框内容
        self.亚马逊.ele('x://input[@class="ant-input-number-input"]').input(str(self.上品数量)) # 输入数量
        self.亚马逊.ele('x://div[@class="ant-modal-content"]//span[text()="确 定"]').click()  # 点击确定
    def 检查批量上品(self):
        print('检查批量上品是否正常运行')
        start_time = time.time()
        while True:
            if time.time() - start_time > 180:  # 超过180秒则退出循环
                return False
            if len(self.亚马逊.eles('x://div[@class="ant-message-notice-content"]')) > 0:
                print('批量上品正常运行种......')
                return True
    def 初始化所有标签(self):
        bqs = self.llq.tab_ids
        print(bqs)
        for bq in bqs:
            tab = self.llq.get_tab(bq)
            print(f"标签页ID: {bq}, 名称: {tab.title}")
            if tab.title == "卖家中心" and self.卖家中心 is None:
                self.卖家中心 = tab
            elif tab.title == "大卖家" and self.大卖家 is None:
                self.大卖家 = tab
            elif re.match(r"^Amazon\.com", tab.title) and self.亚马逊 is None:
                self.亚马逊 = tab
            else:
                tab.close()
        self.检查卖家中心()
        self.检查亚马逊()
        self.检查大卖家()
    def 检查卖家中心(self):

        if self.卖家中心 is None:
            self.卖家中心 = self.llq.new_tab(url="https://seller.kuajingmaihuo.com/login") # 如果卖家中心标签未找到，则创建一个新的标签页
            time.sleep(2)

        if self.卖家中心.ele('x://span[text()="确认缴费"]', timeout=3):  # 检查是否有确认缴费按钮
            return True

        if self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]'): # 检查是否有登录按钮
            self.卖家中心.ele('x://div[@data-testid="beast-core-checkbox-checkIcon"]').click() # 勾选同意
            time.sleep(1)
            self.卖家中心.ele('x://button[@data-testid="beast-core-button"]/span[text()="登录"]').click()
    def 检查大卖家(self):

        if self.大卖家 is None:
            self.大卖家 = self.llq.new_tab(url="chrome-extension://olngdolageopicdnifleobajagpljpdc/nav.html")  # 如果大卖家标签未找到，则创建一个新的标签页
            time.sleep(2)

        while self.大卖家.ele('x://div[@class="ant-space-item"]/button/span[text()="再次重试"]') and not self.大卖家.ele('x://button[@class="css-1p3hq3p ant-btn ant-btn-default"]/span[text()="自动核价"]'):
            self.大卖家.ele('x://div[@class="ant-space-item"]/button/span[text()="再次重试"]').click()
            time.sleep(1)
    def 检查亚马逊(self):
        if self.亚马逊 is None:
            self.亚马逊= self.llq.new_tab(url="https://www.amazon.com/")  # 如果亚马逊标签未找到，则创建一个新的标签页
    def openBrowser(self,id):  # 直接指定ID打开窗口，也可以使用 createBrowser 方法返回的ID

        json_data = {"id": f'{id}'}
        res = requests.post("http://127.0.0.1:54345/browser/open",data=json.dumps(json_data), headers= {'Content-Type': 'application/json'}).json()
        http = res['data']['http']
        print(http)
        llq = ChromiumPage(addr_or_opts=http)
        return llq  # 返回一个浏览器对象

    def createBrowser(self):  # 创建或者更新窗口，指纹参数 browserFingerPrint 如没有特定需求，只需要指定下内核即可，如果需要更详细的参数，请参考文档
        json_data = {
            'name': 'google',  # 窗口名称
            'remark': '',  # 备注
            'proxyMethod': 2,  # 代理方式 2自定义 3 提取IP
            # 代理类型  ['noproxy', 'http', 'https', 'socks5', 'ssh']
            'proxyType': 'socks5',
            'host': '************',  # 代理主机
            'port': '20901',  # 代理端口
            'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
            "proxyPassword": '02695401',  # 代理密码
            "browserFingerPrint": {  # 指纹对象
                'coreVersion': '124'  # 内核版本，注意，win7/win8/winserver 2012 已经不支持112及以上内核了，无法打开
            }
        }

        res = requests.post(f"{ self.url}/browser/update",
                            data=json.dumps(json_data), headers= self.headers).json()
        browserId = res['data']['id']
        print(browserId)
        return browserId

    def updateBrowser( self,id):  # 更新窗口，支持批量更新和按需更新，ids 传入数组，单独更新只传一个id即可，只传入需要修改的字段即可，比如修改备注，具体字段请参考文档，browserFingerPrint指纹对象不修改，则无需传入
        json_data = {'ids': id, #
                     'remark': '我是一个备注',
                     'proxyType': 'socks5',
                     'host': '*************',  # 代理主机
                     'port': '30641',  # 代理端口
                     'proxyUserName': 'o1tIIoBnwD-zone-star',  # 代理账号
                     "proxyPassword": '02695401',  # 代理密码
                     "browserFingerPrint": {
                         "coreVersion": "130",
                         "ostype": "PC",
                         "os": "Win32",
                         "osVersion": "11,10"
                     },
                }
        res = requests.post(f"{ self.url}/browser/update/partial",
                            data=json.dumps(json_data), headers= self.headers).json()
        print(res)

    def closeBrowser( self,id):  # 关闭窗口
        json_data = {'id': f'{id}'}
        requests.post(f"{ self.url}/browser/close",
                      data=json.dumps(json_data), headers= self.headers).json()

    def deleteBrowser( self,id):  # 删除窗口
        json_data = {'id': f'{id}'}
        print(requests.post(f"{ self.url}/browser/delete",
              data=json.dumps(json_data), headers= self.headers).json())
# 创建浏览器实例
llq = 浏览器('2969a7a195054eaa92f5a70efbeb623b')
# 直接调用搜索方法
llq.搜索()  # 搜索关键词为 "Triathlon"，价格范围为 10 到 100

# 启动批量上品
llq.启动批量上品()  # 启动批量上品，数量为 200
# 检查批量上品是否正常运行
if llq.检查批量上品():
    print("批量上品正常运行")